[{"input": {"input": "What color was Cotton?", "output": "white", "expected": "white"}, "expected": 1, "metadata": {"source": "mctest", "story": "Once upon a time, in a barn near a farm house, there lived a little white kitten named <PERSON>. <PERSON> lived high up in a nice warm place above the barn where all of the farmer's horses slept. But <PERSON> wasn't alone in her little home above the barn, oh no. She shared her hay bed with her mommy and 5 other sisters. All of her sisters were cute and fluffy, like <PERSON>. But she was the only white one in the bunch. The rest of her sisters were all orange with beautiful white tiger stripes like <PERSON>'s mommy. Being different made <PERSON> quite sad. She often wished she looked like the rest of her family. So one day, when <PERSON> found a can of the old farmer's orange paint, she used it to paint herself like them. When her mommy and sisters found her they started laughing. \n\n\"What are you doing, <PERSON>?!\" \n\n\"I only wanted to be more like you\". \n\n<PERSON>'s mommy rubbed her face on <PERSON>'s and said \"Oh <PERSON>, but your fur is so pretty and special, like you. We would never want you to be any other way\". And with that, <PERSON>'s mommy picked her up and dropped her into a big bucket of water. When <PERSON> came out she was herself again. Her sisters licked her face until <PERSON>'s fur was all all dry. \n\n\"Don't ever do that again, <PERSON>!\" they all cried. \"Next time you might mess up that pretty white fur of yours and we wouldn't want that!\" \n\nThen <PERSON> thought, \"I change my mind. I like being special\".", "questions": ["What color was Cotton?", "Where did she live?", "Did she live alone?", "Who did she live with?", "What color were her sisters?", "Was <PERSON> happy that she looked different than the rest of her family?", "What did she do to try to make herself the same color as her sisters?", "Whose paint was it?", "What did <PERSON>'s mother and siblings do when they saw her painted orange?", "Where did <PERSON>'s mother put her to clean the paint off?", "What did the other cats do when <PERSON> emerged from the bucket of water?", "Did they want <PERSON> to change the color of her fur?"], "answers": {"input_text": ["white", "in a barn", "no", "with her mommy and 5 sisters", "orange and white", "no", "she painted herself", "the farmer", "they started laughing", "a bucket of water", "licked her face", "no"], "answer_start": [59, 18, 196, 281, 428, 512, 678, 647, 718, 1035, 1143, 965], "answer_end": [93, 80, 215, 315, 490, 549, 716, 676, 776, 1097, 1170, 1008]}}}, {"input": {"input": "What color was Cotton?", "output": "in a barn", "expected": "white"}, "expected": 0, "metadata": {"source": "mctest", "story": "Once upon a time, in a barn near a farm house, there lived a little white kitten named <PERSON>. <PERSON> lived high up in a nice warm place above the barn where all of the farmer's horses slept. But <PERSON> wasn't alone in her little home above the barn, oh no. She shared her hay bed with her mommy and 5 other sisters. All of her sisters were cute and fluffy, like <PERSON>. But she was the only white one in the bunch. The rest of her sisters were all orange with beautiful white tiger stripes like <PERSON>'s mommy. Being different made <PERSON> quite sad. She often wished she looked like the rest of her family. So one day, when <PERSON> found a can of the old farmer's orange paint, she used it to paint herself like them. When her mommy and sisters found her they started laughing. \n\n\"What are you doing, <PERSON>?!\" \n\n\"I only wanted to be more like you\". \n\n<PERSON>'s mommy rubbed her face on <PERSON>'s and said \"Oh <PERSON>, but your fur is so pretty and special, like you. We would never want you to be any other way\". And with that, <PERSON>'s mommy picked her up and dropped her into a big bucket of water. When <PERSON> came out she was herself again. Her sisters licked her face until <PERSON>'s fur was all all dry. \n\n\"Don't ever do that again, <PERSON>!\" they all cried. \"Next time you might mess up that pretty white fur of yours and we wouldn't want that!\" \n\nThen <PERSON> thought, \"I change my mind. I like being special\".", "questions": ["What color was Cotton?", "Where did she live?", "Did she live alone?", "Who did she live with?", "What color were her sisters?", "Was <PERSON> happy that she looked different than the rest of her family?", "What did she do to try to make herself the same color as her sisters?", "Whose paint was it?", "What did <PERSON>'s mother and siblings do when they saw her painted orange?", "Where did <PERSON>'s mother put her to clean the paint off?", "What did the other cats do when <PERSON> emerged from the bucket of water?", "Did they want <PERSON> to change the color of her fur?"], "answers": {"input_text": ["white", "in a barn", "no", "with her mommy and 5 sisters", "orange and white", "no", "she painted herself", "the farmer", "they started laughing", "a bucket of water", "licked her face", "no"], "answer_start": [59, 18, 196, 281, 428, 512, 678, 647, 718, 1035, 1143, 965], "answer_end": [93, 80, 215, 315, 490, 549, 716, 676, 776, 1097, 1170, 1008]}}}, {"input": {"input": "What color was Cotton?", "output": "in a barn white no", "expected": "white"}, "expected": 0.6, "metadata": {"source": "mctest", "story": "Once upon a time, in a barn near a farm house, there lived a little white kitten named <PERSON>. <PERSON> lived high up in a nice warm place above the barn where all of the farmer's horses slept. But <PERSON> wasn't alone in her little home above the barn, oh no. She shared her hay bed with her mommy and 5 other sisters. All of her sisters were cute and fluffy, like <PERSON>. But she was the only white one in the bunch. The rest of her sisters were all orange with beautiful white tiger stripes like <PERSON>'s mommy. Being different made <PERSON> quite sad. She often wished she looked like the rest of her family. So one day, when <PERSON> found a can of the old farmer's orange paint, she used it to paint herself like them. When her mommy and sisters found her they started laughing. \n\n\"What are you doing, <PERSON>?!\" \n\n\"I only wanted to be more like you\". \n\n<PERSON>'s mommy rubbed her face on <PERSON>'s and said \"Oh <PERSON>, but your fur is so pretty and special, like you. We would never want you to be any other way\". And with that, <PERSON>'s mommy picked her up and dropped her into a big bucket of water. When <PERSON> came out she was herself again. Her sisters licked her face until <PERSON>'s fur was all all dry. \n\n\"Don't ever do that again, <PERSON>!\" they all cried. \"Next time you might mess up that pretty white fur of yours and we wouldn't want that!\" \n\nThen <PERSON> thought, \"I change my mind. I like being special\".", "questions": ["What color was Cotton?", "Where did she live?", "Did she live alone?", "Who did she live with?", "What color were her sisters?", "Was <PERSON> happy that she looked different than the rest of her family?", "What did she do to try to make herself the same color as her sisters?", "Whose paint was it?", "What did <PERSON>'s mother and siblings do when they saw her painted orange?", "Where did <PERSON>'s mother put her to clean the paint off?", "What did the other cats do when <PERSON> emerged from the bucket of water?", "Did they want <PERSON> to change the color of her fur?"], "answers": {"input_text": ["white", "in a barn", "no", "with her mommy and 5 sisters", "orange and white", "no", "she painted herself", "the farmer", "they started laughing", "a bucket of water", "licked her face", "no"], "answer_start": [59, 18, 196, 281, 428, 512, 678, 647, 718, 1035, 1143, 965], "answer_end": [93, 80, 215, 315, 490, 549, 716, 676, 776, 1097, 1170, 1008]}}}, {"input": {"input": "what was the name of the fish", "output": "Asta.", "expected": "Asta."}, "expected": 1, "metadata": {"source": "mctest", "story": "Once there was a beautiful fish named <PERSON><PERSON>. <PERSON><PERSON> lived in the ocean. There were lots of other fish in the ocean where <PERSON><PERSON> lived. They played all day long. \n\nOne day, a bottle floated by over the heads of <PERSON><PERSON> and his friends. They looked up and saw the bottle. \"What is it?\" said <PERSON><PERSON>'s friend <PERSON><PERSON>. \"It looks like a bird's belly,\" said <PERSON><PERSON>. But when they swam closer, it was not a bird's belly. It was hard and clear, and there was something inside it. \n\nThe bottle floated above them. They wanted to open it. They wanted to see what was inside. So they caught the bottle and carried it down to the bottom of the ocean. They cracked it open on a rock. When they got it open, they found what was inside. It was a note. The note was written in orange crayon on white paper. <PERSON><PERSON> could not read the note. <PERSON><PERSON> could not read the note. They took the note to <PERSON><PERSON>'s papa. \"What does it say?\" they asked. \n\n<PERSON><PERSON>'s papa read the note. He told <PERSON><PERSON> and <PERSON><PERSON>, \"This note is from a little girl. She wants to be your friend. If you want to be her friend, we can write a note to her. But you have to find another bottle so we can send it to her.\" And that is what they did.", "questions": ["what was the name of the fish", "What looked like a birds belly", "who said that", "Was <PERSON><PERSON> a friend?", "did they get the bottle?", "What was in it", "Did a little boy write the note", "Who could read the note", "What did they do with the note", "did they write back", "were they excited"], "answers": {"input_text": ["Asta.", "a bottle", "Asta.", "Yes", "Yes", "a note", "No", "<PERSON><PERSON>'s papa", "unknown", "yes", "unknown"], "answer_start": [37, 167, 303, 281, 552, 708, 964, 910, -1, 1056, -1], "answer_end": [43, 175, 346, 302, 577, 723, 995, 935, -1, 1173, -1]}}}, {"input": {"input": "what was the name of the fish", "output": "a bottle", "expected": "Asta."}, "expected": 0, "metadata": {"source": "mctest", "story": "Once there was a beautiful fish named <PERSON><PERSON>. <PERSON><PERSON> lived in the ocean. There were lots of other fish in the ocean where <PERSON><PERSON> lived. They played all day long. \n\nOne day, a bottle floated by over the heads of <PERSON><PERSON> and his friends. They looked up and saw the bottle. \"What is it?\" said <PERSON><PERSON>'s friend <PERSON><PERSON>. \"It looks like a bird's belly,\" said <PERSON><PERSON>. But when they swam closer, it was not a bird's belly. It was hard and clear, and there was something inside it. \n\nThe bottle floated above them. They wanted to open it. They wanted to see what was inside. So they caught the bottle and carried it down to the bottom of the ocean. They cracked it open on a rock. When they got it open, they found what was inside. It was a note. The note was written in orange crayon on white paper. <PERSON><PERSON> could not read the note. <PERSON><PERSON> could not read the note. They took the note to <PERSON><PERSON>'s papa. \"What does it say?\" they asked. \n\n<PERSON><PERSON>'s papa read the note. He told <PERSON><PERSON> and <PERSON><PERSON>, \"This note is from a little girl. She wants to be your friend. If you want to be her friend, we can write a note to her. But you have to find another bottle so we can send it to her.\" And that is what they did.", "questions": ["what was the name of the fish", "What looked like a birds belly", "who said that", "Was <PERSON><PERSON> a friend?", "did they get the bottle?", "What was in it", "Did a little boy write the note", "Who could read the note", "What did they do with the note", "did they write back", "were they excited"], "answers": {"input_text": ["Asta.", "a bottle", "Asta.", "Yes", "Yes", "a note", "No", "<PERSON><PERSON>'s papa", "unknown", "yes", "unknown"], "answer_start": [37, 167, 303, 281, 552, 708, 964, 910, -1, 1056, -1], "answer_end": [43, 175, 346, 302, 577, 723, 995, 935, -1, 1173, -1]}}}, {"input": {"input": "what was the name of the fish", "output": "a bottle Asta. Asta.", "expected": "Asta."}, "expected": 0.6, "metadata": {"source": "mctest", "story": "Once there was a beautiful fish named <PERSON><PERSON>. <PERSON><PERSON> lived in the ocean. There were lots of other fish in the ocean where <PERSON><PERSON> lived. They played all day long. \n\nOne day, a bottle floated by over the heads of <PERSON><PERSON> and his friends. They looked up and saw the bottle. \"What is it?\" said <PERSON><PERSON>'s friend <PERSON><PERSON>. \"It looks like a bird's belly,\" said <PERSON><PERSON>. But when they swam closer, it was not a bird's belly. It was hard and clear, and there was something inside it. \n\nThe bottle floated above them. They wanted to open it. They wanted to see what was inside. So they caught the bottle and carried it down to the bottom of the ocean. They cracked it open on a rock. When they got it open, they found what was inside. It was a note. The note was written in orange crayon on white paper. <PERSON><PERSON> could not read the note. <PERSON><PERSON> could not read the note. They took the note to <PERSON><PERSON>'s papa. \"What does it say?\" they asked. \n\n<PERSON><PERSON>'s papa read the note. He told <PERSON><PERSON> and <PERSON><PERSON>, \"This note is from a little girl. She wants to be your friend. If you want to be her friend, we can write a note to her. But you have to find another bottle so we can send it to her.\" And that is what they did.", "questions": ["what was the name of the fish", "What looked like a birds belly", "who said that", "Was <PERSON><PERSON> a friend?", "did they get the bottle?", "What was in it", "Did a little boy write the note", "Who could read the note", "What did they do with the note", "did they write back", "were they excited"], "answers": {"input_text": ["Asta.", "a bottle", "Asta.", "Yes", "Yes", "a note", "No", "<PERSON><PERSON>'s papa", "unknown", "yes", "unknown"], "answer_start": [37, 167, 303, 281, 552, 708, 964, 910, -1, 1056, -1], "answer_end": [43, 175, 346, 302, 577, 723, 995, 935, -1, 1173, -1]}}}, {"input": {"input": "Who is at the door?", "output": "An elderly Chinese lady and a little boy", "expected": "An elderly Chinese lady and a little boy"}, "expected": 1, "metadata": {"source": "race", "story": "My doorbell rings. On the step, I find the elderly Chinese lady, small and slight, holding the hand of a little boy. In her other hand, she holds a paper carrier bag. \n\nI know this lady. It is not her first visit. She is the boy's grandmother, and her daughter bought the house next door last October. \n\nHer daughter, <PERSON>, speaks fluent English. But she is now in Shanghai, and her parents are here with the little boy. <PERSON> has obviously told her mother that I am having heart surgery soon, so her mother has decided I need more nutrients. \n\nI know what is inside the bag--a thermos with hot soup and a stainless-steel container with rice, vegetables and either chicken, meat or shrimp, sometimes with a kind of pancake. This has become an almost-daily practice. \n\nCommunication between us is somewhat affected by the fact that she doesn't speak English and all I can say in Chinese is hello. Once, she brought an iPad as well as the food. She pointed to the screen, which displayed a message from her daughter telling me that her mother wanted to know if the food was all right and whether it was too salty. I am not used to iPads, so she indicated I should go with her to her house. Then, she handed the iPad to her husband and almost immediately I found myself looking at <PERSON> in Shanghai and discussing her mother's cooking and salt intake. Instantly, tears welled in my eyes. \n\n\"Your mother just can't be bringing me meals like this all the time,\" I insisted. \"I can hardly do dishes in return.\" \n\n\"Oh, no, <PERSON>.\" <PERSON> said. \"Mum doesn't like western food. Don't worry about it; she has to cook for the three of them anyway, and she wants to do it.\" \n\nThe doorbell keeps ringing and there is the familiar brown paper carrier bag, handed smilingly to me. \n\nI am now working on some more Chinese words--it's the least I can do after such display of kindness. \n\n\"Thank you\" is, of course, the first one. Somehow, it seems inadequate.", "questions": ["Who is at the door?", "Is she carrying something?", "What?", "Do I know her?", "Who is her daughter?", "Where does <PERSON> live?", "How is she related to the boy?", "What is in the bag?", "Has she done this before?", "Why?", "What has helped us communicate?", "What kind of dishes does she bring?", "What do I do to help communicate with her?", "Do she continue bringing the bag?", "What is the first phrase I learn?"], "answers": {"input_text": ["An elderly Chinese lady and a little boy", "Yes", "a paper carrier bag", "Yes", "<PERSON>", "Shanghai", "mother", "food", "Yes", "I am having heart surgery soon, so her mother has decided I need more nutrients", "an iPad", "hot soup and a container with rice, vegetables and either chicken, meat or shrimp, sometimes with a kind of pancake", "I am now working on some more Chinese words", "Yes", "\"Thank you\""], "answer_start": [19, 136, 146, 169, 304, 348, 214, 579, 727, 423, 917, 594, 1771, 1667, 1874], "answer_end": [115, 165, 165, 185, 324, 375, 302, 725, 769, 544, 924, 725, 1814, 1769, 1885]}}}, {"input": {"input": "Who is at the door?", "output": "Yes", "expected": "An elderly Chinese lady and a little boy"}, "expected": 0, "metadata": {"source": "race", "story": "My doorbell rings. On the step, I find the elderly Chinese lady, small and slight, holding the hand of a little boy. In her other hand, she holds a paper carrier bag. \n\nI know this lady. It is not her first visit. She is the boy's grandmother, and her daughter bought the house next door last October. \n\nHer daughter, <PERSON>, speaks fluent English. But she is now in Shanghai, and her parents are here with the little boy. <PERSON> has obviously told her mother that I am having heart surgery soon, so her mother has decided I need more nutrients. \n\nI know what is inside the bag--a thermos with hot soup and a stainless-steel container with rice, vegetables and either chicken, meat or shrimp, sometimes with a kind of pancake. This has become an almost-daily practice. \n\nCommunication between us is somewhat affected by the fact that she doesn't speak English and all I can say in Chinese is hello. Once, she brought an iPad as well as the food. She pointed to the screen, which displayed a message from her daughter telling me that her mother wanted to know if the food was all right and whether it was too salty. I am not used to iPads, so she indicated I should go with her to her house. Then, she handed the iPad to her husband and almost immediately I found myself looking at <PERSON> in Shanghai and discussing her mother's cooking and salt intake. Instantly, tears welled in my eyes. \n\n\"Your mother just can't be bringing me meals like this all the time,\" I insisted. \"I can hardly do dishes in return.\" \n\n\"Oh, no, <PERSON>.\" <PERSON> said. \"Mum doesn't like western food. Don't worry about it; she has to cook for the three of them anyway, and she wants to do it.\" \n\nThe doorbell keeps ringing and there is the familiar brown paper carrier bag, handed smilingly to me. \n\nI am now working on some more Chinese words--it's the least I can do after such display of kindness. \n\n\"Thank you\" is, of course, the first one. Somehow, it seems inadequate.", "questions": ["Who is at the door?", "Is she carrying something?", "What?", "Do I know her?", "Who is her daughter?", "Where does <PERSON> live?", "How is she related to the boy?", "What is in the bag?", "Has she done this before?", "Why?", "What has helped us communicate?", "What kind of dishes does she bring?", "What do I do to help communicate with her?", "Do she continue bringing the bag?", "What is the first phrase I learn?"], "answers": {"input_text": ["An elderly Chinese lady and a little boy", "Yes", "a paper carrier bag", "Yes", "<PERSON>", "Shanghai", "mother", "food", "Yes", "I am having heart surgery soon, so her mother has decided I need more nutrients", "an iPad", "hot soup and a container with rice, vegetables and either chicken, meat or shrimp, sometimes with a kind of pancake", "I am now working on some more Chinese words", "Yes", "\"Thank you\""], "answer_start": [19, 136, 146, 169, 304, 348, 214, 579, 727, 423, 917, 594, 1771, 1667, 1874], "answer_end": [115, 165, 165, 185, 324, 375, 302, 725, 769, 544, 924, 725, 1814, 1769, 1885]}}}, {"input": {"input": "Who is at the door?", "output": "Yes An elderly Chinese lady and a little boy a paper carrier bag", "expected": "An elderly Chinese lady and a little boy"}, "expected": 0.6, "metadata": {"source": "race", "story": "My doorbell rings. On the step, I find the elderly Chinese lady, small and slight, holding the hand of a little boy. In her other hand, she holds a paper carrier bag. \n\nI know this lady. It is not her first visit. She is the boy's grandmother, and her daughter bought the house next door last October. \n\nHer daughter, <PERSON>, speaks fluent English. But she is now in Shanghai, and her parents are here with the little boy. <PERSON> has obviously told her mother that I am having heart surgery soon, so her mother has decided I need more nutrients. \n\nI know what is inside the bag--a thermos with hot soup and a stainless-steel container with rice, vegetables and either chicken, meat or shrimp, sometimes with a kind of pancake. This has become an almost-daily practice. \n\nCommunication between us is somewhat affected by the fact that she doesn't speak English and all I can say in Chinese is hello. Once, she brought an iPad as well as the food. She pointed to the screen, which displayed a message from her daughter telling me that her mother wanted to know if the food was all right and whether it was too salty. I am not used to iPads, so she indicated I should go with her to her house. Then, she handed the iPad to her husband and almost immediately I found myself looking at <PERSON> in Shanghai and discussing her mother's cooking and salt intake. Instantly, tears welled in my eyes. \n\n\"Your mother just can't be bringing me meals like this all the time,\" I insisted. \"I can hardly do dishes in return.\" \n\n\"Oh, no, <PERSON>.\" <PERSON> said. \"Mum doesn't like western food. Don't worry about it; she has to cook for the three of them anyway, and she wants to do it.\" \n\nThe doorbell keeps ringing and there is the familiar brown paper carrier bag, handed smilingly to me. \n\nI am now working on some more Chinese words--it's the least I can do after such display of kindness. \n\n\"Thank you\" is, of course, the first one. Somehow, it seems inadequate.", "questions": ["Who is at the door?", "Is she carrying something?", "What?", "Do I know her?", "Who is her daughter?", "Where does <PERSON> live?", "How is she related to the boy?", "What is in the bag?", "Has she done this before?", "Why?", "What has helped us communicate?", "What kind of dishes does she bring?", "What do I do to help communicate with her?", "Do she continue bringing the bag?", "What is the first phrase I learn?"], "answers": {"input_text": ["An elderly Chinese lady and a little boy", "Yes", "a paper carrier bag", "Yes", "<PERSON>", "Shanghai", "mother", "food", "Yes", "I am having heart surgery soon, so her mother has decided I need more nutrients", "an iPad", "hot soup and a container with rice, vegetables and either chicken, meat or shrimp, sometimes with a kind of pancake", "I am now working on some more Chinese words", "Yes", "\"Thank you\""], "answer_start": [19, 136, 146, 169, 304, 348, 214, 579, 727, 423, 917, 594, 1771, 1667, 1874], "answer_end": [115, 165, 165, 185, 324, 375, 302, 725, 769, 544, 924, 725, 1814, 1769, 1885]}}}, {"input": {"input": "Is someone in showbiz?", "output": "Yes.", "expected": "Yes."}, "expected": 1, "metadata": {"source": "cnn", "story": "(CNN) -- <PERSON>, the dapper, mustachioed cop-turned-actor best known for his tough-as-nails work in such TV series as \"Law & Order,\" \"Crime Story,\" and \"Miami Vice,\" has died. He was 69. \n\n\"We are deeply saddened by the loss of a great actor and a wonderful man,\" said his publicist, <PERSON>, in a statement Monday. \"<PERSON> was always warmhearted and professional, with a great sense of humor and passion for his profession. He will be greatly missed by his family, friends and colleagues.\" \n\n<PERSON><PERSON>, who had a long career as a police officer in Chicago, got into acting through director <PERSON>, who used him as a consultant and cast him in his 1981 movie, \"Thief.\" That role led to others in such Mann-created shows as \"Miami Vice\" (in which <PERSON><PERSON> played a mobster) and \"Crime Story\" (in which he starred as Lt. <PERSON>). \n\n<PERSON><PERSON> also had roles, generally as either cops or gangsters, in a number of movies, including \"Midnight Run\" (1988), \"Get Shorty\" (1995), \"The Mod Squad\" (1999) and \"Snatch\" (2000). \n\nIn 2004, he joined the cast of the long-running \"Law & Order\" after <PERSON>'s departure, playing Detective <PERSON>, a role he reprised on the spinoff \"Trial by Jury.\" <PERSON><PERSON><PERSON> was known for flashy clothes and an expensive car, a distinct counterpoint to <PERSON><PERSON>'s rumpled <PERSON><PERSON>. \n\n<PERSON><PERSON> was on \"Law & Order\" for two years, partnered with <PERSON>'s <PERSON>'s character became a senior detective after <PERSON><PERSON> left the show. ", "questions": ["Is someone in showbiz?", "Whom?", "What did he do?", "Is he still alive?", "Was he in movies?", "Anything recent?", "What happened in the early 80's?", "Who cast him?", "What was the title of the movie?", "What parts did he usually get?", "What happened in 2004?", "Which one?", "Who did he portray?", "Did he have a beater for a car?", "What did he have?", "Were the characters clothes frumpy?", "What were they like?", "Was he on the show for five years?", "Was he always an actor?", "What had he been before?"], "answers": {"input_text": ["Yes.", "<PERSON>", "Actor", "No", "Yes", "No", "<PERSON><PERSON> was cast in a film", "<PERSON>", "\"Thief\"", "cops or gangsters", "He joined a TV show cast.", "\"Law & Order\"", "Detective <PERSON>", "No", "An expensive car", "No", "Flashy", "No", "No", "A cop"], "answer_start": [8, 8, 8, 9, 512, 856, 573, 512, 512, 856, 1041, 1041, 1041, 1217, 1216, 1217, 1217, 1338, 8, 8], "answer_end": [181, 183, 66, 181, 690, 1039, 690, 662, 690, 1039, 1103, 1134, 1165, 1336, 1336, 1336, 1336, 1381, 138, 65]}}}, {"input": {"input": "Is someone in showbiz?", "output": "<PERSON>", "expected": "Yes."}, "expected": 0, "metadata": {"source": "cnn", "story": "(CNN) -- <PERSON>, the dapper, mustachioed cop-turned-actor best known for his tough-as-nails work in such TV series as \"Law & Order,\" \"Crime Story,\" and \"Miami Vice,\" has died. He was 69. \n\n\"We are deeply saddened by the loss of a great actor and a wonderful man,\" said his publicist, <PERSON>, in a statement Monday. \"<PERSON> was always warmhearted and professional, with a great sense of humor and passion for his profession. He will be greatly missed by his family, friends and colleagues.\" \n\n<PERSON><PERSON>, who had a long career as a police officer in Chicago, got into acting through director <PERSON>, who used him as a consultant and cast him in his 1981 movie, \"Thief.\" That role led to others in such Mann-created shows as \"Miami Vice\" (in which <PERSON><PERSON> played a mobster) and \"Crime Story\" (in which he starred as Lt. <PERSON>). \n\n<PERSON><PERSON> also had roles, generally as either cops or gangsters, in a number of movies, including \"Midnight Run\" (1988), \"Get Shorty\" (1995), \"The Mod Squad\" (1999) and \"Snatch\" (2000). \n\nIn 2004, he joined the cast of the long-running \"Law & Order\" after <PERSON>'s departure, playing Detective <PERSON>, a role he reprised on the spinoff \"Trial by Jury.\" <PERSON><PERSON><PERSON> was known for flashy clothes and an expensive car, a distinct counterpoint to <PERSON><PERSON>'s rumpled <PERSON><PERSON>. \n\n<PERSON><PERSON> was on \"Law & Order\" for two years, partnered with <PERSON>'s <PERSON>'s character became a senior detective after <PERSON><PERSON> left the show. ", "questions": ["Is someone in showbiz?", "Whom?", "What did he do?", "Is he still alive?", "Was he in movies?", "Anything recent?", "What happened in the early 80's?", "Who cast him?", "What was the title of the movie?", "What parts did he usually get?", "What happened in 2004?", "Which one?", "Who did he portray?", "Did he have a beater for a car?", "What did he have?", "Were the characters clothes frumpy?", "What were they like?", "Was he on the show for five years?", "Was he always an actor?", "What had he been before?"], "answers": {"input_text": ["Yes.", "<PERSON>", "Actor", "No", "Yes", "No", "<PERSON><PERSON> was cast in a film", "<PERSON>", "\"Thief\"", "cops or gangsters", "He joined a TV show cast.", "\"Law & Order\"", "Detective <PERSON>", "No", "An expensive car", "No", "Flashy", "No", "No", "A cop"], "answer_start": [8, 8, 8, 9, 512, 856, 573, 512, 512, 856, 1041, 1041, 1041, 1217, 1216, 1217, 1217, 1338, 8, 8], "answer_end": [181, 183, 66, 181, 690, 1039, 690, 662, 690, 1039, 1103, 1134, 1165, 1336, 1336, 1336, 1336, 1381, 138, 65]}}}, {"input": {"input": "Is someone in showbiz?", "output": "<PERSON>. Actor", "expected": "Yes."}, "expected": 0.6, "metadata": {"source": "cnn", "story": "(CNN) -- <PERSON>, the dapper, mustachioed cop-turned-actor best known for his tough-as-nails work in such TV series as \"Law & Order,\" \"Crime Story,\" and \"Miami Vice,\" has died. He was 69. \n\n\"We are deeply saddened by the loss of a great actor and a wonderful man,\" said his publicist, <PERSON>, in a statement Monday. \"<PERSON> was always warmhearted and professional, with a great sense of humor and passion for his profession. He will be greatly missed by his family, friends and colleagues.\" \n\n<PERSON><PERSON>, who had a long career as a police officer in Chicago, got into acting through director <PERSON>, who used him as a consultant and cast him in his 1981 movie, \"Thief.\" That role led to others in such Mann-created shows as \"Miami Vice\" (in which <PERSON><PERSON> played a mobster) and \"Crime Story\" (in which he starred as Lt. <PERSON>). \n\n<PERSON><PERSON> also had roles, generally as either cops or gangsters, in a number of movies, including \"Midnight Run\" (1988), \"Get Shorty\" (1995), \"The Mod Squad\" (1999) and \"Snatch\" (2000). \n\nIn 2004, he joined the cast of the long-running \"Law & Order\" after <PERSON>'s departure, playing Detective <PERSON>, a role he reprised on the spinoff \"Trial by Jury.\" <PERSON><PERSON><PERSON> was known for flashy clothes and an expensive car, a distinct counterpoint to <PERSON><PERSON>'s rumpled <PERSON><PERSON>. \n\n<PERSON><PERSON> was on \"Law & Order\" for two years, partnered with <PERSON>'s <PERSON>'s character became a senior detective after <PERSON><PERSON> left the show. ", "questions": ["Is someone in showbiz?", "Whom?", "What did he do?", "Is he still alive?", "Was he in movies?", "Anything recent?", "What happened in the early 80's?", "Who cast him?", "What was the title of the movie?", "What parts did he usually get?", "What happened in 2004?", "Which one?", "Who did he portray?", "Did he have a beater for a car?", "What did he have?", "Were the characters clothes frumpy?", "What were they like?", "Was he on the show for five years?", "Was he always an actor?", "What had he been before?"], "answers": {"input_text": ["Yes.", "<PERSON>", "Actor", "No", "Yes", "No", "<PERSON><PERSON> was cast in a film", "<PERSON>", "\"Thief\"", "cops or gangsters", "He joined a TV show cast.", "\"Law & Order\"", "Detective <PERSON>", "No", "An expensive car", "No", "Flashy", "No", "No", "A cop"], "answer_start": [8, 8, 8, 9, 512, 856, 573, 512, 512, 856, 1041, 1041, 1041, 1217, 1216, 1217, 1217, 1338, 8, 8], "answer_end": [181, 183, 66, 181, 690, 1039, 690, 662, 690, 1039, 1103, 1134, 1165, 1336, 1336, 1336, 1336, 1381, 138, 65]}}}, {"input": {"input": "Where do <PERSON><PERSON><PERSON> and <PERSON> travel to and from every day?", "output": "school", "expected": "school"}, "expected": 1, "metadata": {"source": "mctest", "story": "<PERSON> and <PERSON><PERSON><PERSON> travel to and from school every day. <PERSON> lives further from the bus stop than <PERSON><PERSON><PERSON> does, stops every morning at <PERSON><PERSON><PERSON>'s house to join him to walk to the bus stop. Every afternoon, after school, when walking home from the bus stop they go in for cookies and milk that <PERSON><PERSON><PERSON>'s mother has ready and waiting for them. <PERSON><PERSON><PERSON> can't eat cheese or cake so they had the same snack every day. They both work together on their homework and when they are done they play together. <PERSON> always makes sure to leave in time to get home for dinner. She doesn't want to miss story time which was right before bedtime. \n\nOne morning <PERSON> walked up to <PERSON><PERSON><PERSON>'s house, she thought something might be wrong because normally <PERSON><PERSON><PERSON> was waiting outside for her and on this morning he was not to be found. <PERSON> went up to the door and knocked. She waited and waited and yet no one answered. She saw that <PERSON><PERSON><PERSON>'s mother's car wasn't in their driveway which was weird. She waited for a few bit looking up and down the block and getting worried when <PERSON><PERSON><PERSON> was nowhere to be found. \n\n<PERSON> didn't want to miss the bus to school and hurried off to make it in time. The bus driver saw that she was upset and that <PERSON><PERSON><PERSON> was not with her that morning. She told him what happened and he said that he was sure that everything would be okay. \n\n<PERSON> got to school, ran to her teacher and told him what happened that morning. The teacher smiled and told her not to worry, <PERSON><PERSON><PERSON>'s mother had called and he was going to the dentist and would be at school after lunch and that she would see him at the bus stop like normal tomorrow.", "questions": ["Where do <PERSON><PERSON><PERSON> and <PERSON> travel to and from every day?", "Does <PERSON><PERSON><PERSON> live further from the bus stop?", "What do they do every afternoon after school?", "Does <PERSON><PERSON><PERSON> eat cheese?", "Do they play before their homework?", "What does <PERSON> not want to miss?", "When is that?", "What happened when <PERSON> knocked on <PERSON><PERSON><PERSON>'s door?", "Did she see the car?", "Did she miss the bus?", "What did the bus driver see?", "Did <PERSON> tell him why?", "What did he say?", "When she got to school, who did she tell?", "Did he frown?", "Who had called?", "Where did he go?", "When would he be back?"], "answers": {"input_text": ["school", "No", "go to <PERSON>'s house", "No", "No", "story time", "right before bedtime", "no one answered", "No", "No", "that she was upset", "yes", "everything would be okay", "her teacher", "No", "<PERSON><PERSON><PERSON>'s mother", "to the dentist", "yes"], "answer_start": [0, 56, 190, 343, 414, 565, 601, 888, 906, 1098, 1179, 1265, 1296, 1382, 1436, 1481, 1514, 1546], "answer_end": [54, 107, 341, 367, 497, 600, 631, 903, 965, 1177, 1216, 1291, 1350, 1434, 1454, 1509, 1541, 1576]}}}, {"input": {"input": "Where do <PERSON><PERSON><PERSON> and <PERSON> travel to and from every day?", "output": "No", "expected": "school"}, "expected": 0, "metadata": {"source": "mctest", "story": "<PERSON> and <PERSON><PERSON><PERSON> travel to and from school every day. <PERSON> lives further from the bus stop than <PERSON><PERSON><PERSON> does, stops every morning at <PERSON><PERSON><PERSON>'s house to join him to walk to the bus stop. Every afternoon, after school, when walking home from the bus stop they go in for cookies and milk that <PERSON><PERSON><PERSON>'s mother has ready and waiting for them. <PERSON><PERSON><PERSON> can't eat cheese or cake so they had the same snack every day. They both work together on their homework and when they are done they play together. <PERSON> always makes sure to leave in time to get home for dinner. She doesn't want to miss story time which was right before bedtime. \n\nOne morning <PERSON> walked up to <PERSON><PERSON><PERSON>'s house, she thought something might be wrong because normally <PERSON><PERSON><PERSON> was waiting outside for her and on this morning he was not to be found. <PERSON> went up to the door and knocked. She waited and waited and yet no one answered. She saw that <PERSON><PERSON><PERSON>'s mother's car wasn't in their driveway which was weird. She waited for a few bit looking up and down the block and getting worried when <PERSON><PERSON><PERSON> was nowhere to be found. \n\n<PERSON> didn't want to miss the bus to school and hurried off to make it in time. The bus driver saw that she was upset and that <PERSON><PERSON><PERSON> was not with her that morning. She told him what happened and he said that he was sure that everything would be okay. \n\n<PERSON> got to school, ran to her teacher and told him what happened that morning. The teacher smiled and told her not to worry, <PERSON><PERSON><PERSON>'s mother had called and he was going to the dentist and would be at school after lunch and that she would see him at the bus stop like normal tomorrow.", "questions": ["Where do <PERSON><PERSON><PERSON> and <PERSON> travel to and from every day?", "Does <PERSON><PERSON><PERSON> live further from the bus stop?", "What do they do every afternoon after school?", "Does <PERSON><PERSON><PERSON> eat cheese?", "Do they play before their homework?", "What does <PERSON> not want to miss?", "When is that?", "What happened when <PERSON> knocked on <PERSON><PERSON><PERSON>'s door?", "Did she see the car?", "Did she miss the bus?", "What did the bus driver see?", "Did <PERSON> tell him why?", "What did he say?", "When she got to school, who did she tell?", "Did he frown?", "Who had called?", "Where did he go?", "When would he be back?"], "answers": {"input_text": ["school", "No", "go to <PERSON>'s house", "No", "No", "story time", "right before bedtime", "no one answered", "No", "No", "that she was upset", "yes", "everything would be okay", "her teacher", "No", "<PERSON><PERSON><PERSON>'s mother", "to the dentist", "yes"], "answer_start": [0, 56, 190, 343, 414, 565, 601, 888, 906, 1098, 1179, 1265, 1296, 1382, 1436, 1481, 1514, 1546], "answer_end": [54, 107, 341, 367, 497, 600, 631, 903, 965, 1177, 1216, 1291, 1350, 1434, 1454, 1509, 1541, 1576]}}}, {"input": {"input": "Where do <PERSON><PERSON><PERSON> and <PERSON> travel to and from every day?", "output": "No school go to <PERSON>'s house", "expected": "school"}, "expected": 0.6, "metadata": {"source": "mctest", "story": "<PERSON> and <PERSON><PERSON><PERSON> travel to and from school every day. <PERSON> lives further from the bus stop than <PERSON><PERSON><PERSON> does, stops every morning at <PERSON><PERSON><PERSON>'s house to join him to walk to the bus stop. Every afternoon, after school, when walking home from the bus stop they go in for cookies and milk that <PERSON><PERSON><PERSON>'s mother has ready and waiting for them. <PERSON><PERSON><PERSON> can't eat cheese or cake so they had the same snack every day. They both work together on their homework and when they are done they play together. <PERSON> always makes sure to leave in time to get home for dinner. She doesn't want to miss story time which was right before bedtime. \n\nOne morning <PERSON> walked up to <PERSON><PERSON><PERSON>'s house, she thought something might be wrong because normally <PERSON><PERSON><PERSON> was waiting outside for her and on this morning he was not to be found. <PERSON> went up to the door and knocked. She waited and waited and yet no one answered. She saw that <PERSON><PERSON><PERSON>'s mother's car wasn't in their driveway which was weird. She waited for a few bit looking up and down the block and getting worried when <PERSON><PERSON><PERSON> was nowhere to be found. \n\n<PERSON> didn't want to miss the bus to school and hurried off to make it in time. The bus driver saw that she was upset and that <PERSON><PERSON><PERSON> was not with her that morning. She told him what happened and he said that he was sure that everything would be okay. \n\n<PERSON> got to school, ran to her teacher and told him what happened that morning. The teacher smiled and told her not to worry, <PERSON><PERSON><PERSON>'s mother had called and he was going to the dentist and would be at school after lunch and that she would see him at the bus stop like normal tomorrow.", "questions": ["Where do <PERSON><PERSON><PERSON> and <PERSON> travel to and from every day?", "Does <PERSON><PERSON><PERSON> live further from the bus stop?", "What do they do every afternoon after school?", "Does <PERSON><PERSON><PERSON> eat cheese?", "Do they play before their homework?", "What does <PERSON> not want to miss?", "When is that?", "What happened when <PERSON> knocked on <PERSON><PERSON><PERSON>'s door?", "Did she see the car?", "Did she miss the bus?", "What did the bus driver see?", "Did <PERSON> tell him why?", "What did he say?", "When she got to school, who did she tell?", "Did he frown?", "Who had called?", "Where did he go?", "When would he be back?"], "answers": {"input_text": ["school", "No", "go to <PERSON>'s house", "No", "No", "story time", "right before bedtime", "no one answered", "No", "No", "that she was upset", "yes", "everything would be okay", "her teacher", "No", "<PERSON><PERSON><PERSON>'s mother", "to the dentist", "yes"], "answer_start": [0, 56, 190, 343, 414, 565, 601, 888, 906, 1098, 1179, 1265, 1296, 1382, 1436, 1481, 1514, 1546], "answer_end": [54, 107, 341, 367, 497, 600, 631, 903, 965, 1177, 1216, 1291, 1350, 1434, 1454, 1509, 1541, 1576]}}}, {"input": {"input": "How many burroughs are there?", "output": "five", "expected": "five"}, "expected": 1, "metadata": {"source": "wikipedia", "story": "Staten Island is one of the five boroughs of New York City in the U.S. state of New York. In the southwest of the city, Staten Island is the southernmost part of both the city and state of New York, with Conference House Park at the southern tip of the island and the state. The borough is separated from New Jersey by the Arthur Kill and the Kill Van Kull, and from the rest of New York by New York Bay. With a 2016 Census-estimated population of 476,015, Staten Island is the least populated of the boroughs but is the third-largest in area at . Staten Island is the only borough of New York with a non-Hispanic White majority. The borough is coextensive with Richmond County, and until 1975 was the Borough of Richmond. Its flag was later changed to reflect this. Staten Island has been sometimes called \"the forgotten borough\" by inhabitants who feel neglected by the city government. \n\nThe North Shore—especially the neighborhoods of St. George, Tompkinsville, Clifton, and Stapleton—is the most urban part of the island; it contains the designated St. George Historic District and the St. Paul's Avenue-Stapleton Heights Historic District, which feature large Victorian houses. The East Shore is home to the F.D.R. Boardwalk, the fourth-longest in the world. The South Shore, site of the 17th-century Dutch and French Huguenot settlement, developed rapidly beginning in the 1960s and 1970s and is now mostly suburban in character. The West Shore is the least populated and most industrial part of the island.", "questions": ["How many burroughs are there?", "in what city?", "and state?", "Is staten island one?", "Where is it?", "What separates it from new jersey?", "What is its population?", "Is it the most populated?", "what ethnicity is the majority?", "What is it sometimes called?", "why?", "what is the most urban part?", "which neighborhoods?"], "answers": {"input_text": ["five", "New York City", "New York", "Yes", "In the southwest of the city", "<PERSON> and the Kill Van Kull", "476,015", "no", "non-Hispanic White", "the forgotten borough", "because the inhabitants feel neglected by the city government", "North Shore", "St. George, Tompkinsville, Clifton, and Stapleton"], "answer_start": [28, 45, 80, 17, 88, 322, 448, 477, 600, 808, 831, 894, 939], "answer_end": [32, 58, 88, 21, 119, 356, 455, 493, 620, 829, 890, 906, 988]}}}, {"input": {"input": "How many burroughs are there?", "output": "New York City", "expected": "five"}, "expected": 0, "metadata": {"source": "wikipedia", "story": "Staten Island is one of the five boroughs of New York City in the U.S. state of New York. In the southwest of the city, Staten Island is the southernmost part of both the city and state of New York, with Conference House Park at the southern tip of the island and the state. The borough is separated from New Jersey by the Arthur Kill and the Kill Van Kull, and from the rest of New York by New York Bay. With a 2016 Census-estimated population of 476,015, Staten Island is the least populated of the boroughs but is the third-largest in area at . Staten Island is the only borough of New York with a non-Hispanic White majority. The borough is coextensive with Richmond County, and until 1975 was the Borough of Richmond. Its flag was later changed to reflect this. Staten Island has been sometimes called \"the forgotten borough\" by inhabitants who feel neglected by the city government. \n\nThe North Shore—especially the neighborhoods of St. George, Tompkinsville, Clifton, and Stapleton—is the most urban part of the island; it contains the designated St. George Historic District and the St. Paul's Avenue-Stapleton Heights Historic District, which feature large Victorian houses. The East Shore is home to the F.D.R. Boardwalk, the fourth-longest in the world. The South Shore, site of the 17th-century Dutch and French Huguenot settlement, developed rapidly beginning in the 1960s and 1970s and is now mostly suburban in character. The West Shore is the least populated and most industrial part of the island.", "questions": ["How many burroughs are there?", "in what city?", "and state?", "Is staten island one?", "Where is it?", "What separates it from new jersey?", "What is its population?", "Is it the most populated?", "what ethnicity is the majority?", "What is it sometimes called?", "why?", "what is the most urban part?", "which neighborhoods?"], "answers": {"input_text": ["five", "New York City", "New York", "Yes", "In the southwest of the city", "<PERSON> and the Kill Van Kull", "476,015", "no", "non-Hispanic White", "the forgotten borough", "because the inhabitants feel neglected by the city government", "North Shore", "St. George, Tompkinsville, Clifton, and Stapleton"], "answer_start": [28, 45, 80, 17, 88, 322, 448, 477, 600, 808, 831, 894, 939], "answer_end": [32, 58, 88, 21, 119, 356, 455, 493, 620, 829, 890, 906, 988]}}}, {"input": {"input": "How many burroughs are there?", "output": "New York City five New York", "expected": "five"}, "expected": 0.6, "metadata": {"source": "wikipedia", "story": "Staten Island is one of the five boroughs of New York City in the U.S. state of New York. In the southwest of the city, Staten Island is the southernmost part of both the city and state of New York, with Conference House Park at the southern tip of the island and the state. The borough is separated from New Jersey by the Arthur Kill and the Kill Van Kull, and from the rest of New York by New York Bay. With a 2016 Census-estimated population of 476,015, Staten Island is the least populated of the boroughs but is the third-largest in area at . Staten Island is the only borough of New York with a non-Hispanic White majority. The borough is coextensive with Richmond County, and until 1975 was the Borough of Richmond. Its flag was later changed to reflect this. Staten Island has been sometimes called \"the forgotten borough\" by inhabitants who feel neglected by the city government. \n\nThe North Shore—especially the neighborhoods of St. George, Tompkinsville, Clifton, and Stapleton—is the most urban part of the island; it contains the designated St. George Historic District and the St. Paul's Avenue-Stapleton Heights Historic District, which feature large Victorian houses. The East Shore is home to the F.D.R. Boardwalk, the fourth-longest in the world. The South Shore, site of the 17th-century Dutch and French Huguenot settlement, developed rapidly beginning in the 1960s and 1970s and is now mostly suburban in character. The West Shore is the least populated and most industrial part of the island.", "questions": ["How many burroughs are there?", "in what city?", "and state?", "Is staten island one?", "Where is it?", "What separates it from new jersey?", "What is its population?", "Is it the most populated?", "what ethnicity is the majority?", "What is it sometimes called?", "why?", "what is the most urban part?", "which neighborhoods?"], "answers": {"input_text": ["five", "New York City", "New York", "Yes", "In the southwest of the city", "<PERSON> and the Kill Van Kull", "476,015", "no", "non-Hispanic White", "the forgotten borough", "because the inhabitants feel neglected by the city government", "North Shore", "St. George, Tompkinsville, Clifton, and Stapleton"], "answer_start": [28, 45, 80, 17, 88, 322, 448, 477, 600, 808, 831, 894, 939], "answer_end": [32, 58, 88, 21, 119, 356, 455, 493, 620, 829, 890, 906, 988]}}}, {"input": {"input": "When did <PERSON> wake up?", "output": "Five in the morning", "expected": "Five in the morning"}, "expected": 1, "metadata": {"source": "race", "story": "Thunder was coming when <PERSON> woke up at five in the morning. He checked the weather forecast. A violent storm was coming ,but it sounded like his small town wouldn't be hit too hard. But <PERSON><PERSON><PERSON>, a firefighter, had clearly known the power of these huge storms from experiences. \"Do you know where the flashlights are?\" he asked his wife. <PERSON>. Just then, thunder was all-around them. The moment he turned the flashlight on. The house lights went off. A second later, the kitchen windows were broken. <PERSON><PERSON><PERSON> and <PERSON> ran to their boys who were still sleeping in their bedroom. \n\n\"Get up, get up, R.J.! \" <PERSON><PERSON><PERSON> shouted, waving his flashlight. The sleepy boy moved to the edge of the bed. <PERSON><PERSON><PERSON> held out his arms and ordered his son to jump. He was too late. The roof was torn down. R.J. was buried ,under the pieces. \n\n\"I've lost him,\" <PERSON><PERSON><PERSON> thought. Quickly, he hurried to <PERSON> to shield him. Glass, wood, and plaster ( ) hit them. Then something huge, heavy-maybe the washing machine-knocked into him. He hurt his arms, but he still held the flashlight in one hand. \n\nAfter a long period, the wind began to die down. <PERSON><PERSON><PERSON> found himself standing in the ruins of his home. Darkness lay all about him. Then he thought he saw a shape moving straight toward him. It was <PERSON><PERSON><PERSON><PERSON>, guided home by the light of his father's flashlight. \n\nAt the hospital later, <PERSON><PERSON><PERSON><PERSON> described what had happened to him. \"I rushed out when the wall started moving I was scared. My mom and dad were gone. Pieces of glass hit my back, and something hit my neck really hard. \" \n\n<PERSON><PERSON>. had been raised up into the air by the wind and dropped back to the ground. <PERSON>ly, <PERSON>.<PERSON>. was not hurt badly. <PERSON> all his family, <PERSON>ppes was hurt most seriously.", "questions": ["When did <PERSON> wake up?", "What was the first thing he checked?", "Was there a storm headed that way?", "What was his profession?", "Did the house lights go out?", "What device did they use when the power went out?", "Who was buried under the roof?", "Who did he need to shield?", "What were they hit by?", "Was <PERSON><PERSON> badly hurt?", "Who was hurt the worst?", "What guided R<PERSON> home?"], "answers": {"input_text": ["Five in the morning", "Weather forecast", "Yes", "Firefighter", "Yes", "Flashlight", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Glass, wood, plaster, and maybe the washing machine", "No", "<PERSON><PERSON><PERSON>", "The flashlight"], "answer_start": [24, 71, 104, 193, 396, 396, 771, 864, 907, 1641, 1696, 1274], "answer_end": [69, 102, 130, 217, 461, 461, 829, 906, 1016, 1677, 1726, 1339]}}}, {"input": {"input": "When did <PERSON> wake up?", "output": "Weather forecast", "expected": "Five in the morning"}, "expected": 0, "metadata": {"source": "race", "story": "Thunder was coming when <PERSON> woke up at five in the morning. He checked the weather forecast. A violent storm was coming ,but it sounded like his small town wouldn't be hit too hard. But <PERSON><PERSON><PERSON>, a firefighter, had clearly known the power of these huge storms from experiences. \"Do you know where the flashlights are?\" he asked his wife. <PERSON>. Just then, thunder was all-around them. The moment he turned the flashlight on. The house lights went off. A second later, the kitchen windows were broken. <PERSON><PERSON><PERSON> and <PERSON> ran to their boys who were still sleeping in their bedroom. \n\n\"Get up, get up, R.J.! \" <PERSON><PERSON><PERSON> shouted, waving his flashlight. The sleepy boy moved to the edge of the bed. <PERSON><PERSON><PERSON> held out his arms and ordered his son to jump. He was too late. The roof was torn down. R.J. was buried ,under the pieces. \n\n\"I've lost him,\" <PERSON><PERSON><PERSON> thought. Quickly, he hurried to <PERSON> to shield him. Glass, wood, and plaster ( ) hit them. Then something huge, heavy-maybe the washing machine-knocked into him. He hurt his arms, but he still held the flashlight in one hand. \n\nAfter a long period, the wind began to die down. <PERSON><PERSON><PERSON> found himself standing in the ruins of his home. Darkness lay all about him. Then he thought he saw a shape moving straight toward him. It was <PERSON><PERSON><PERSON><PERSON>, guided home by the light of his father's flashlight. \n\nAt the hospital later, <PERSON><PERSON><PERSON><PERSON> described what had happened to him. \"I rushed out when the wall started moving I was scared. My mom and dad were gone. Pieces of glass hit my back, and something hit my neck really hard. \" \n\n<PERSON><PERSON>. had been raised up into the air by the wind and dropped back to the ground. <PERSON>ly, <PERSON>.<PERSON>. was not hurt badly. <PERSON> all his family, <PERSON>ppes was hurt most seriously.", "questions": ["When did <PERSON> wake up?", "What was the first thing he checked?", "Was there a storm headed that way?", "What was his profession?", "Did the house lights go out?", "What device did they use when the power went out?", "Who was buried under the roof?", "Who did he need to shield?", "What were they hit by?", "Was <PERSON><PERSON> badly hurt?", "Who was hurt the worst?", "What guided R<PERSON> home?"], "answers": {"input_text": ["Five in the morning", "Weather forecast", "Yes", "Firefighter", "Yes", "Flashlight", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Glass, wood, plaster, and maybe the washing machine", "No", "<PERSON><PERSON><PERSON>", "The flashlight"], "answer_start": [24, 71, 104, 193, 396, 396, 771, 864, 907, 1641, 1696, 1274], "answer_end": [69, 102, 130, 217, 461, 461, 829, 906, 1016, 1677, 1726, 1339]}}}, {"input": {"input": "When did <PERSON> wake up?", "output": "Weather forecast Five in the morning Yes", "expected": "Five in the morning"}, "expected": 0.6, "metadata": {"source": "race", "story": "Thunder was coming when <PERSON> woke up at five in the morning. He checked the weather forecast. A violent storm was coming ,but it sounded like his small town wouldn't be hit too hard. But <PERSON><PERSON><PERSON>, a firefighter, had clearly known the power of these huge storms from experiences. \"Do you know where the flashlights are?\" he asked his wife. <PERSON>. Just then, thunder was all-around them. The moment he turned the flashlight on. The house lights went off. A second later, the kitchen windows were broken. <PERSON><PERSON><PERSON> and <PERSON> ran to their boys who were still sleeping in their bedroom. \n\n\"Get up, get up, R.J.! \" <PERSON><PERSON><PERSON> shouted, waving his flashlight. The sleepy boy moved to the edge of the bed. <PERSON><PERSON><PERSON> held out his arms and ordered his son to jump. He was too late. The roof was torn down. R.J. was buried ,under the pieces. \n\n\"I've lost him,\" <PERSON><PERSON><PERSON> thought. Quickly, he hurried to <PERSON> to shield him. Glass, wood, and plaster ( ) hit them. Then something huge, heavy-maybe the washing machine-knocked into him. He hurt his arms, but he still held the flashlight in one hand. \n\nAfter a long period, the wind began to die down. <PERSON><PERSON><PERSON> found himself standing in the ruins of his home. Darkness lay all about him. Then he thought he saw a shape moving straight toward him. It was <PERSON><PERSON><PERSON><PERSON>, guided home by the light of his father's flashlight. \n\nAt the hospital later, <PERSON><PERSON><PERSON><PERSON> described what had happened to him. \"I rushed out when the wall started moving I was scared. My mom and dad were gone. Pieces of glass hit my back, and something hit my neck really hard. \" \n\n<PERSON><PERSON>. had been raised up into the air by the wind and dropped back to the ground. <PERSON>ly, <PERSON>.<PERSON>. was not hurt badly. <PERSON> all his family, <PERSON>ppes was hurt most seriously.", "questions": ["When did <PERSON> wake up?", "What was the first thing he checked?", "Was there a storm headed that way?", "What was his profession?", "Did the house lights go out?", "What device did they use when the power went out?", "Who was buried under the roof?", "Who did he need to shield?", "What were they hit by?", "Was <PERSON><PERSON> badly hurt?", "Who was hurt the worst?", "What guided R<PERSON> home?"], "answers": {"input_text": ["Five in the morning", "Weather forecast", "Yes", "Firefighter", "Yes", "Flashlight", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Glass, wood, plaster, and maybe the washing machine", "No", "<PERSON><PERSON><PERSON>", "The flashlight"], "answer_start": [24, 71, 104, 193, 396, 396, 771, 864, 907, 1641, 1696, 1274], "answer_end": [69, 102, 130, 217, 461, 461, 829, 906, 1016, 1677, 1726, 1339]}}}, {"input": {"input": "Whose house was searched?", "output": "<PERSON>", "expected": "<PERSON>"}, "expected": 1, "metadata": {"source": "cnn", "story": "(CNN) -- FBI agents on Friday night searched the Maryland home of the suspect in the recent disappearance of an American woman in Aruba, an agent said. \n\nThe search is occurring in the Gaithersburg residence of <PERSON>, who is currently being held in an Aruban jail, FBI Special Agent <PERSON> told CNN. \n\nAgents, wearing vests that said FBI and carrying empty cardboard and plastic boxes, arrived about 8:40 p.m. Friday. About 15 unmarked cars could be seen on the street, as well as a Montgomery County police vehicle. \n\nSupervisory Special Agent <PERSON>, who was at the residence, declined to comment further on the search, citing the active investigation. \n\nAruban Solicitor General <PERSON><PERSON> said earlier Friday that the suspect will appear in court Monday, where an investigating magistrate could order him held for at least eight more days, order him to remain on the island or release him outright due to a lack of evidence. \n\n<PERSON><PERSON><PERSON><PERSON> was arrested by Aruban police on August 5, three days after <PERSON><PERSON> was last seen near Baby Beach on the western tip of the Caribbean island. \n\n<PERSON><PERSON><PERSON><PERSON> told authorities that he had been snorkeling with <PERSON> when he signaled to her to swim back, according to a statement. When he reached the beach, <PERSON> was nowhere to be found, <PERSON><PERSON><PERSON><PERSON> allegedly said. \n\nThe area that <PERSON><PERSON><PERSON><PERSON> led authorities to is a rocky, unsightly location that locals say is not a popular snorkeling spot. \n\nAlthough prosecutors have continued to identify the 50-year-old American man by his initials, <PERSON><PERSON><PERSON>, they also released a photo of a man who appears to be <PERSON><PERSON><PERSON><PERSON>. His attorney, <PERSON>, also has said that his client is being held as a suspect in <PERSON>'s death. <PERSON> has not returned telephone calls seeking comment. ", "questions": ["Whose house was searched?", "In what city?", "County?", "State?", "Where is he now?", "Why?", "What organization is doing the search?", "How many unmarked vehicles were there?", "Who spoke for the Aruban government?", "When will <PERSON><PERSON><PERSON><PERSON> go to court?", "How many days could he be held?", "Who went missing?", "Where?", "What was she doing?", "With whom?", "Did she return safely?", "Is the beach a good snorkeling place?", "How old is he?", "When was he arrested?", "How many were snorkeling?"], "answers": {"input_text": ["<PERSON>", "Gaithersburg", "Montgomery County", "Maryland", "Aruban jail", "suspect in the recent disappearance of an American woman", "FBI", "15", "Aruban Solicitor General <PERSON><PERSON>", "Monday", "at least eight more days", "<PERSON><PERSON>", "ast seen near Baby Beach", "snorkeling", "<PERSON><PERSON><PERSON><PERSON>", "No, <PERSON> was nowhere to be found", "locals say is not a popular snorkeling spot.", "50", "August 5", "2, <PERSON><PERSON><PERSON><PERSON> told authorities that he had been snorkeling with <PERSON>"], "answer_start": [211, 184, 494, 48, 260, 70, 345, 435, 678, 772, 839, 1021, 1040, 1154, 1111, 1269, 1407, 1506, 993, 1111], "answer_end": [224, 197, 511, 57, 271, 126, 348, 437, 713, 778, 863, 1033, 1064, 1164, 1119, 1300, 1452, 1508, 1002, 1177]}}}, {"input": {"input": "Whose house was searched?", "output": "Gaithersburg", "expected": "<PERSON>"}, "expected": 0, "metadata": {"source": "cnn", "story": "(CNN) -- FBI agents on Friday night searched the Maryland home of the suspect in the recent disappearance of an American woman in Aruba, an agent said. \n\nThe search is occurring in the Gaithersburg residence of <PERSON>, who is currently being held in an Aruban jail, FBI Special Agent <PERSON> told CNN. \n\nAgents, wearing vests that said FBI and carrying empty cardboard and plastic boxes, arrived about 8:40 p.m. Friday. About 15 unmarked cars could be seen on the street, as well as a Montgomery County police vehicle. \n\nSupervisory Special Agent <PERSON>, who was at the residence, declined to comment further on the search, citing the active investigation. \n\nAruban Solicitor General <PERSON><PERSON> said earlier Friday that the suspect will appear in court Monday, where an investigating magistrate could order him held for at least eight more days, order him to remain on the island or release him outright due to a lack of evidence. \n\n<PERSON><PERSON><PERSON><PERSON> was arrested by Aruban police on August 5, three days after <PERSON><PERSON> was last seen near Baby Beach on the western tip of the Caribbean island. \n\n<PERSON><PERSON><PERSON><PERSON> told authorities that he had been snorkeling with <PERSON> when he signaled to her to swim back, according to a statement. When he reached the beach, <PERSON> was nowhere to be found, <PERSON><PERSON><PERSON><PERSON> allegedly said. \n\nThe area that <PERSON><PERSON><PERSON><PERSON> led authorities to is a rocky, unsightly location that locals say is not a popular snorkeling spot. \n\nAlthough prosecutors have continued to identify the 50-year-old American man by his initials, <PERSON><PERSON><PERSON>, they also released a photo of a man who appears to be <PERSON><PERSON><PERSON><PERSON>. His attorney, <PERSON>, also has said that his client is being held as a suspect in <PERSON>'s death. <PERSON> has not returned telephone calls seeking comment. ", "questions": ["Whose house was searched?", "In what city?", "County?", "State?", "Where is he now?", "Why?", "What organization is doing the search?", "How many unmarked vehicles were there?", "Who spoke for the Aruban government?", "When will <PERSON><PERSON><PERSON><PERSON> go to court?", "How many days could he be held?", "Who went missing?", "Where?", "What was she doing?", "With whom?", "Did she return safely?", "Is the beach a good snorkeling place?", "How old is he?", "When was he arrested?", "How many were snorkeling?"], "answers": {"input_text": ["<PERSON>", "Gaithersburg", "Montgomery County", "Maryland", "Aruban jail", "suspect in the recent disappearance of an American woman", "FBI", "15", "Aruban Solicitor General <PERSON><PERSON>", "Monday", "at least eight more days", "<PERSON><PERSON>", "ast seen near Baby Beach", "snorkeling", "<PERSON><PERSON><PERSON><PERSON>", "No, <PERSON> was nowhere to be found", "locals say is not a popular snorkeling spot.", "50", "August 5", "2, <PERSON><PERSON><PERSON><PERSON> told authorities that he had been snorkeling with <PERSON>"], "answer_start": [211, 184, 494, 48, 260, 70, 345, 435, 678, 772, 839, 1021, 1040, 1154, 1111, 1269, 1407, 1506, 993, 1111], "answer_end": [224, 197, 511, 57, 271, 126, 348, 437, 713, 778, 863, 1033, 1064, 1164, 1119, 1300, 1452, 1508, 1002, 1177]}}}, {"input": {"input": "Whose house was searched?", "output": "Gaithersburg Gary <PERSON>dano Montgomery County", "expected": "<PERSON>"}, "expected": 0.6, "metadata": {"source": "cnn", "story": "(CNN) -- FBI agents on Friday night searched the Maryland home of the suspect in the recent disappearance of an American woman in Aruba, an agent said. \n\nThe search is occurring in the Gaithersburg residence of <PERSON>, who is currently being held in an Aruban jail, FBI Special Agent <PERSON> told CNN. \n\nAgents, wearing vests that said FBI and carrying empty cardboard and plastic boxes, arrived about 8:40 p.m. Friday. About 15 unmarked cars could be seen on the street, as well as a Montgomery County police vehicle. \n\nSupervisory Special Agent <PERSON>, who was at the residence, declined to comment further on the search, citing the active investigation. \n\nAruban Solicitor General <PERSON><PERSON> said earlier Friday that the suspect will appear in court Monday, where an investigating magistrate could order him held for at least eight more days, order him to remain on the island or release him outright due to a lack of evidence. \n\n<PERSON><PERSON><PERSON><PERSON> was arrested by Aruban police on August 5, three days after <PERSON><PERSON> was last seen near Baby Beach on the western tip of the Caribbean island. \n\n<PERSON><PERSON><PERSON><PERSON> told authorities that he had been snorkeling with <PERSON> when he signaled to her to swim back, according to a statement. When he reached the beach, <PERSON> was nowhere to be found, <PERSON><PERSON><PERSON><PERSON> allegedly said. \n\nThe area that <PERSON><PERSON><PERSON><PERSON> led authorities to is a rocky, unsightly location that locals say is not a popular snorkeling spot. \n\nAlthough prosecutors have continued to identify the 50-year-old American man by his initials, <PERSON><PERSON><PERSON>, they also released a photo of a man who appears to be <PERSON><PERSON><PERSON><PERSON>. His attorney, <PERSON>, also has said that his client is being held as a suspect in <PERSON>'s death. <PERSON> has not returned telephone calls seeking comment. ", "questions": ["Whose house was searched?", "In what city?", "County?", "State?", "Where is he now?", "Why?", "What organization is doing the search?", "How many unmarked vehicles were there?", "Who spoke for the Aruban government?", "When will <PERSON><PERSON><PERSON><PERSON> go to court?", "How many days could he be held?", "Who went missing?", "Where?", "What was she doing?", "With whom?", "Did she return safely?", "Is the beach a good snorkeling place?", "How old is he?", "When was he arrested?", "How many were snorkeling?"], "answers": {"input_text": ["<PERSON>", "Gaithersburg", "Montgomery County", "Maryland", "Aruban jail", "suspect in the recent disappearance of an American woman", "FBI", "15", "Aruban Solicitor General <PERSON><PERSON>", "Monday", "at least eight more days", "<PERSON><PERSON>", "ast seen near Baby Beach", "snorkeling", "<PERSON><PERSON><PERSON><PERSON>", "No, <PERSON> was nowhere to be found", "locals say is not a popular snorkeling spot.", "50", "August 5", "2, <PERSON><PERSON><PERSON><PERSON> told authorities that he had been snorkeling with <PERSON>"], "answer_start": [211, 184, 494, 48, 260, 70, 345, 435, 678, 772, 839, 1021, 1040, 1154, 1111, 1269, 1407, 1506, 993, 1111], "answer_end": [224, 197, 511, 57, 271, 126, 348, 437, 713, 778, 863, 1033, 1064, 1164, 1119, 1300, 1452, 1508, 1002, 1177]}}}, {"input": {"input": "Which country consumes tea the most?", "output": "Great Britain", "expected": "Great Britain"}, "expected": 1, "metadata": {"source": "race", "story": "Which country grows the most tea? The answer is India. It grows three times as much as China. Which country drinks the most tea? It's neither China nor Japan. It's Great Britain. In the wild, tea plants may be 30 feet tall. But a plant grown for market is pruned. Pruning keeps the plant only three or four feet tall. This is an easy height for tea picking. Only the two top leaves and bud of each new shoot are picked. So to make money, tea plantations must be huge. In general, there are two kinds of tea. Black tea and green tea. Black tea is fermented. In the process, the tea loses nearly all of its healthy qualities. Green tea is steamed right after the leaves are picked. Green tea _ its healthy qualities. For example, it may prevent heart disease. How did we get tea bag? The answer: by accident. Tea merchants used to send samples in tin boxes. This was costly. One merchant thought of a cheaper way. He sent samples in small silk bags. Customers would cut open the bag. They would brew the leaves as usual. One customer put the bag into a pot. Then he just poured hot water over it. And the tea bag was born. <PERSON> was the first to drink tea. (<PERSON> was a Chinese emperor.) This was about 2737 B.C. Shen had bad digestion. So he drank several cups of hot water daily. One day something happened. Leaves from a wild tea tree fell into the hot water pot. The next cup was poured. The water was now colored. Shen sipped it. He liked it. He drank it all. <PERSON> was proud of his new drink. He served it to his guests. Word spread. People thought this way. Tea is good enough for the Emperor. So it must be good enough for the people. Tea became the drink of China.", "questions": ["Which country consumes tea the most?", "Which country grows it the most?", "How tall is the tea plant?", "What did they do to green tea after picking it?", "What good thing do the tea do to the health?", "How was the tea created?", "Who took the tea first?", "When did he take it?", "Was he happy with it?", "How did his body react to the tea?"], "answers": {"input_text": ["Great Britain", "India.", "may be 30 feet tall", "prune it", "may prevent heart disease.", "by accident", "<PERSON>", "about 2737 B.C", "yes", "unknown"], "answer_start": [159, 34, 196, 224, 731, 794, 1120, 1190, 1436, -1], "answer_end": [177, 54, 222, 264, 757, 805, 1158, 1212, 1448, -1]}}}, {"input": {"input": "Which country consumes tea the most?", "output": "India.", "expected": "Great Britain"}, "expected": 0, "metadata": {"source": "race", "story": "Which country grows the most tea? The answer is India. It grows three times as much as China. Which country drinks the most tea? It's neither China nor Japan. It's Great Britain. In the wild, tea plants may be 30 feet tall. But a plant grown for market is pruned. Pruning keeps the plant only three or four feet tall. This is an easy height for tea picking. Only the two top leaves and bud of each new shoot are picked. So to make money, tea plantations must be huge. In general, there are two kinds of tea. Black tea and green tea. Black tea is fermented. In the process, the tea loses nearly all of its healthy qualities. Green tea is steamed right after the leaves are picked. Green tea _ its healthy qualities. For example, it may prevent heart disease. How did we get tea bag? The answer: by accident. Tea merchants used to send samples in tin boxes. This was costly. One merchant thought of a cheaper way. He sent samples in small silk bags. Customers would cut open the bag. They would brew the leaves as usual. One customer put the bag into a pot. Then he just poured hot water over it. And the tea bag was born. <PERSON> was the first to drink tea. (<PERSON> was a Chinese emperor.) This was about 2737 B.C. Shen had bad digestion. So he drank several cups of hot water daily. One day something happened. Leaves from a wild tea tree fell into the hot water pot. The next cup was poured. The water was now colored. Shen sipped it. He liked it. He drank it all. <PERSON> was proud of his new drink. He served it to his guests. Word spread. People thought this way. Tea is good enough for the Emperor. So it must be good enough for the people. Tea became the drink of China.", "questions": ["Which country consumes tea the most?", "Which country grows it the most?", "How tall is the tea plant?", "What did they do to green tea after picking it?", "What good thing do the tea do to the health?", "How was the tea created?", "Who took the tea first?", "When did he take it?", "Was he happy with it?", "How did his body react to the tea?"], "answers": {"input_text": ["Great Britain", "India.", "may be 30 feet tall", "prune it", "may prevent heart disease.", "by accident", "<PERSON>", "about 2737 B.C", "yes", "unknown"], "answer_start": [159, 34, 196, 224, 731, 794, 1120, 1190, 1436, -1], "answer_end": [177, 54, 222, 264, 757, 805, 1158, 1212, 1448, -1]}}}, {"input": {"input": "Which country consumes tea the most?", "output": "India. Great Britain may be 30 feet tall", "expected": "Great Britain"}, "expected": 0.6, "metadata": {"source": "race", "story": "Which country grows the most tea? The answer is India. It grows three times as much as China. Which country drinks the most tea? It's neither China nor Japan. It's Great Britain. In the wild, tea plants may be 30 feet tall. But a plant grown for market is pruned. Pruning keeps the plant only three or four feet tall. This is an easy height for tea picking. Only the two top leaves and bud of each new shoot are picked. So to make money, tea plantations must be huge. In general, there are two kinds of tea. Black tea and green tea. Black tea is fermented. In the process, the tea loses nearly all of its healthy qualities. Green tea is steamed right after the leaves are picked. Green tea _ its healthy qualities. For example, it may prevent heart disease. How did we get tea bag? The answer: by accident. Tea merchants used to send samples in tin boxes. This was costly. One merchant thought of a cheaper way. He sent samples in small silk bags. Customers would cut open the bag. They would brew the leaves as usual. One customer put the bag into a pot. Then he just poured hot water over it. And the tea bag was born. <PERSON> was the first to drink tea. (<PERSON> was a Chinese emperor.) This was about 2737 B.C. Shen had bad digestion. So he drank several cups of hot water daily. One day something happened. Leaves from a wild tea tree fell into the hot water pot. The next cup was poured. The water was now colored. Shen sipped it. He liked it. He drank it all. <PERSON> was proud of his new drink. He served it to his guests. Word spread. People thought this way. Tea is good enough for the Emperor. So it must be good enough for the people. Tea became the drink of China.", "questions": ["Which country consumes tea the most?", "Which country grows it the most?", "How tall is the tea plant?", "What did they do to green tea after picking it?", "What good thing do the tea do to the health?", "How was the tea created?", "Who took the tea first?", "When did he take it?", "Was he happy with it?", "How did his body react to the tea?"], "answers": {"input_text": ["Great Britain", "India.", "may be 30 feet tall", "prune it", "may prevent heart disease.", "by accident", "<PERSON>", "about 2737 B.C", "yes", "unknown"], "answer_start": [159, 34, 196, 224, 731, 794, 1120, 1190, 1436, -1], "answer_end": [177, 54, 222, 264, 757, 805, 1158, 1212, 1448, -1]}}}, {"input": {"input": "What news agency showed photos of American soldiers?", "output": "Der Spiegel", "expected": "Der Spiegel"}, "expected": 1, "metadata": {"source": "cnn", "story": "Kabul, Afghanistan (CNN) -- The German news outlet <PERSON> has published photographs of what appear to be two U.S. soldiers in Afghanistan posing over the bodies of dead Afghans -- images which threaten to further complicate the American military effort there. \n\nTwo images show the soldiers kneeling by a bloody body sprawled over a patch of sand and grass. A third shows what appears to be two bodies propped up, back to back, against a post in front of a military vehicle. \n\n<PERSON> identifies the soldiers as Spc<PERSON> <PERSON> and Pfc. <PERSON>, who are both facing charges relating to the wrongful deaths of Afghan civilians. \n\nSpecifically, <PERSON> is charged with the premeditated deaths of three civilians, possessing a dismembered human finger, wrongfully possessing photographs of human casualties, and smoking hashish. \n\nHe is also accused of conspiring with <PERSON><PERSON><PERSON> to shoot at a civilian and then toss a grenade so it would look like the soldiers were under attack. \n\n<PERSON><PERSON><PERSON> is charged with three counts of murder. He is accused of killing one Afghan civilian in January 2010 with a grenade and rifle; killing another in May 2010 in a similar manner; and shooting a third to death in February 2010. \n\nU.S. military rules also prohibit \"taking or retaining individual souvenirs or trophies,\" which the photographs may be construed as. \n\nThe trial for the two soldiers is being conducted at Joint Base Lewis-McChord in Washington. <PERSON><PERSON><PERSON>'s court martial is slated to begin Wednesday, while the start date for <PERSON>' court martial has not been publicly announced. ", "questions": ["What news agency showed photos of American soldiers?", "From what country?", "What were the soldiers doing in the photos?", "What was the condition of the body?", "What does another photo show?", "Near what?", "What could the photos be construed as?", "What is the name of one of the soldiers?", "The other?", "What is <PERSON> being charged with?"], "answers": {"input_text": ["Der Spiegel", "Germany", "posing over the bodies of dead Afghans", "bloody", "propped up, back to back", "military vehicle.", "taking or retaining individual souvenirs or trophies", "<PERSON>", "Pfc. <PERSON>", "<PERSON> is charged with the premeditated deaths of three civilians"], "answer_start": [28, 28, 77, 267, 363, 362, 1229, 482, 545, 662], "answer_end": [62, 62, 181, 361, 430, 481, 1362, 541, 563, 727]}}}, {"input": {"input": "What news agency showed photos of American soldiers?", "output": "Germany", "expected": "Der Spiegel"}, "expected": 0, "metadata": {"source": "cnn", "story": "Kabul, Afghanistan (CNN) -- The German news outlet <PERSON> has published photographs of what appear to be two U.S. soldiers in Afghanistan posing over the bodies of dead Afghans -- images which threaten to further complicate the American military effort there. \n\nTwo images show the soldiers kneeling by a bloody body sprawled over a patch of sand and grass. A third shows what appears to be two bodies propped up, back to back, against a post in front of a military vehicle. \n\n<PERSON> identifies the soldiers as Spc<PERSON> <PERSON> and Pfc. <PERSON>, who are both facing charges relating to the wrongful deaths of Afghan civilians. \n\nSpecifically, <PERSON> is charged with the premeditated deaths of three civilians, possessing a dismembered human finger, wrongfully possessing photographs of human casualties, and smoking hashish. \n\nHe is also accused of conspiring with <PERSON><PERSON><PERSON> to shoot at a civilian and then toss a grenade so it would look like the soldiers were under attack. \n\n<PERSON><PERSON><PERSON> is charged with three counts of murder. He is accused of killing one Afghan civilian in January 2010 with a grenade and rifle; killing another in May 2010 in a similar manner; and shooting a third to death in February 2010. \n\nU.S. military rules also prohibit \"taking or retaining individual souvenirs or trophies,\" which the photographs may be construed as. \n\nThe trial for the two soldiers is being conducted at Joint Base Lewis-McChord in Washington. <PERSON><PERSON><PERSON>'s court martial is slated to begin Wednesday, while the start date for <PERSON>' court martial has not been publicly announced. ", "questions": ["What news agency showed photos of American soldiers?", "From what country?", "What were the soldiers doing in the photos?", "What was the condition of the body?", "What does another photo show?", "Near what?", "What could the photos be construed as?", "What is the name of one of the soldiers?", "The other?", "What is <PERSON> being charged with?"], "answers": {"input_text": ["Der Spiegel", "Germany", "posing over the bodies of dead Afghans", "bloody", "propped up, back to back", "military vehicle.", "taking or retaining individual souvenirs or trophies", "<PERSON>", "Pfc. <PERSON>", "<PERSON> is charged with the premeditated deaths of three civilians"], "answer_start": [28, 28, 77, 267, 363, 362, 1229, 482, 545, 662], "answer_end": [62, 62, 181, 361, 430, 481, 1362, 541, 563, 727]}}}, {"input": {"input": "What news agency showed photos of American soldiers?", "output": "Germany Der Spiegel posing over the bodies of dead Afghans", "expected": "Der Spiegel"}, "expected": 0.6, "metadata": {"source": "cnn", "story": "Kabul, Afghanistan (CNN) -- The German news outlet <PERSON> has published photographs of what appear to be two U.S. soldiers in Afghanistan posing over the bodies of dead Afghans -- images which threaten to further complicate the American military effort there. \n\nTwo images show the soldiers kneeling by a bloody body sprawled over a patch of sand and grass. A third shows what appears to be two bodies propped up, back to back, against a post in front of a military vehicle. \n\n<PERSON> identifies the soldiers as Spc<PERSON> <PERSON> and Pfc. <PERSON>, who are both facing charges relating to the wrongful deaths of Afghan civilians. \n\nSpecifically, <PERSON> is charged with the premeditated deaths of three civilians, possessing a dismembered human finger, wrongfully possessing photographs of human casualties, and smoking hashish. \n\nHe is also accused of conspiring with <PERSON><PERSON><PERSON> to shoot at a civilian and then toss a grenade so it would look like the soldiers were under attack. \n\n<PERSON><PERSON><PERSON> is charged with three counts of murder. He is accused of killing one Afghan civilian in January 2010 with a grenade and rifle; killing another in May 2010 in a similar manner; and shooting a third to death in February 2010. \n\nU.S. military rules also prohibit \"taking or retaining individual souvenirs or trophies,\" which the photographs may be construed as. \n\nThe trial for the two soldiers is being conducted at Joint Base Lewis-McChord in Washington. <PERSON><PERSON><PERSON>'s court martial is slated to begin Wednesday, while the start date for <PERSON>' court martial has not been publicly announced. ", "questions": ["What news agency showed photos of American soldiers?", "From what country?", "What were the soldiers doing in the photos?", "What was the condition of the body?", "What does another photo show?", "Near what?", "What could the photos be construed as?", "What is the name of one of the soldiers?", "The other?", "What is <PERSON> being charged with?"], "answers": {"input_text": ["Der Spiegel", "Germany", "posing over the bodies of dead Afghans", "bloody", "propped up, back to back", "military vehicle.", "taking or retaining individual souvenirs or trophies", "<PERSON>", "Pfc. <PERSON>", "<PERSON> is charged with the premeditated deaths of three civilians"], "answer_start": [28, 28, 77, 267, 363, 362, 1229, 482, 545, 662], "answer_end": [62, 62, 181, 361, 430, 481, 1362, 541, 563, 727]}}}, {"input": {"input": "Who are the two boxer featured in this article?", "output": "<PERSON> and <PERSON>", "expected": "<PERSON> and <PERSON>"}, "expected": 1, "metadata": {"source": "cnn", "story": "(CNN)A chiseled boxer's Instagram feed shows him making constant references to the Bible and enjoying gospel singing with his wife. \n\nAnother features his formidable opponent counting stacks of money, hanging out in strip clubs, and flashing diamond watches and Ferraris. \n\nWelcome to the world of boxing promotion, circa 2015. \n\nAmerican <PERSON> and Filipino <PERSON> are set to officially announce their heavily anticipated boxing match at a press conference in Los Angeles Wednesday. \n\nWith the combined purse for the May 2 bout in Las Vegas reported to touch $300 million pending viewership numbers, the incentives to self-promote could not be higher. \n\n\"Nowadays you have to be on social media to launch the fight and to build hype,\" says boxing promoter <PERSON><PERSON>, CEO of Team Sauerland. \"It couldn't be done without it.\" \n\nThirty-eight year old <PERSON><PERSON><PERSON> (47-0, 26 knockouts), who favors the moniker \"The Money Man\" or \"<PERSON><PERSON>\" (The Best Ever), boasts nearly five million Instagram followers, 5.65 million followers on Twitter and 9.2 million Facebook likes. \n\nHe famously confirmed the fight via Shots, a photo sharing social media application that he's invested in, and displays links to his clothing brand, The Money Team, on all his accounts. \n\nAlong with professing to the be the best fighter of all time, he could also stake a claim to be one of the greatest social media users in sports. \n\n\"I think they're both playing their roles,\" says <PERSON><PERSON><PERSON>, who promotes over 45 boxers. \"You've got the bad guy and the good guy, really. You've got the guy who throws the money around (<PERSON><PERSON><PERSON>), that's his image, and <PERSON><PERSON><PERSON>, he's the hope of a nation.\" ", "questions": ["Who are the two boxer featured in this article?", "What is <PERSON><PERSON><PERSON><PERSON> nick name?", "what is the other", "which stand for?", "what is the name of his clothing line?", "Who is <PERSON><PERSON><PERSON>?", "how many people does he promote", "what is the combined purse for this match?", "how old is mayweather?", "what do they say about <PERSON><PERSON><PERSON><PERSON> and his following?"], "answers": {"input_text": ["<PERSON> and <PERSON>", "1 is the money man", "TBE", "The Best Ever", "The Money Team,", "a boxing promoter", "over 45 boxers.", "$300 million pending viewership numbers", "38", "just that it has bible references and shows him enjoying gos[e; singing with his wife"], "answer_start": [330, 851, 851, 945, 1175, 753, 1472, 504, 851, 6], "answer_end": [383, 1085, 971, 970, 1253, 791, 1511, 671, 884, 132]}}}, {"input": {"input": "Who are the two boxer featured in this article?", "output": "1 is the money man", "expected": "<PERSON> and <PERSON>"}, "expected": 0, "metadata": {"source": "cnn", "story": "(CNN)A chiseled boxer's Instagram feed shows him making constant references to the Bible and enjoying gospel singing with his wife. \n\nAnother features his formidable opponent counting stacks of money, hanging out in strip clubs, and flashing diamond watches and Ferraris. \n\nWelcome to the world of boxing promotion, circa 2015. \n\nAmerican <PERSON> and Filipino <PERSON> are set to officially announce their heavily anticipated boxing match at a press conference in Los Angeles Wednesday. \n\nWith the combined purse for the May 2 bout in Las Vegas reported to touch $300 million pending viewership numbers, the incentives to self-promote could not be higher. \n\n\"Nowadays you have to be on social media to launch the fight and to build hype,\" says boxing promoter <PERSON><PERSON>, CEO of Team Sauerland. \"It couldn't be done without it.\" \n\nThirty-eight year old <PERSON><PERSON><PERSON> (47-0, 26 knockouts), who favors the moniker \"The Money Man\" or \"<PERSON><PERSON>\" (The Best Ever), boasts nearly five million Instagram followers, 5.65 million followers on Twitter and 9.2 million Facebook likes. \n\nHe famously confirmed the fight via Shots, a photo sharing social media application that he's invested in, and displays links to his clothing brand, The Money Team, on all his accounts. \n\nAlong with professing to the be the best fighter of all time, he could also stake a claim to be one of the greatest social media users in sports. \n\n\"I think they're both playing their roles,\" says <PERSON><PERSON><PERSON>, who promotes over 45 boxers. \"You've got the bad guy and the good guy, really. You've got the guy who throws the money around (<PERSON><PERSON><PERSON>), that's his image, and <PERSON><PERSON><PERSON>, he's the hope of a nation.\" ", "questions": ["Who are the two boxer featured in this article?", "What is <PERSON><PERSON><PERSON><PERSON> nick name?", "what is the other", "which stand for?", "what is the name of his clothing line?", "Who is <PERSON><PERSON><PERSON>?", "how many people does he promote", "what is the combined purse for this match?", "how old is mayweather?", "what do they say about <PERSON><PERSON><PERSON><PERSON> and his following?"], "answers": {"input_text": ["<PERSON> and <PERSON>", "1 is the money man", "TBE", "The Best Ever", "The Money Team,", "a boxing promoter", "over 45 boxers.", "$300 million pending viewership numbers", "38", "just that it has bible references and shows him enjoying gos[e; singing with his wife"], "answer_start": [330, 851, 851, 945, 1175, 753, 1472, 504, 851, 6], "answer_end": [383, 1085, 971, 970, 1253, 791, 1511, 671, 884, 132]}}}, {"input": {"input": "Who are the two boxer featured in this article?", "output": "1 is the money man <PERSON> and <PERSON>", "expected": "<PERSON> and <PERSON>"}, "expected": 0.6, "metadata": {"source": "cnn", "story": "(CNN)A chiseled boxer's Instagram feed shows him making constant references to the Bible and enjoying gospel singing with his wife. \n\nAnother features his formidable opponent counting stacks of money, hanging out in strip clubs, and flashing diamond watches and Ferraris. \n\nWelcome to the world of boxing promotion, circa 2015. \n\nAmerican <PERSON> and Filipino <PERSON> are set to officially announce their heavily anticipated boxing match at a press conference in Los Angeles Wednesday. \n\nWith the combined purse for the May 2 bout in Las Vegas reported to touch $300 million pending viewership numbers, the incentives to self-promote could not be higher. \n\n\"Nowadays you have to be on social media to launch the fight and to build hype,\" says boxing promoter <PERSON><PERSON>, CEO of Team Sauerland. \"It couldn't be done without it.\" \n\nThirty-eight year old <PERSON><PERSON><PERSON> (47-0, 26 knockouts), who favors the moniker \"The Money Man\" or \"<PERSON><PERSON>\" (The Best Ever), boasts nearly five million Instagram followers, 5.65 million followers on Twitter and 9.2 million Facebook likes. \n\nHe famously confirmed the fight via Shots, a photo sharing social media application that he's invested in, and displays links to his clothing brand, The Money Team, on all his accounts. \n\nAlong with professing to the be the best fighter of all time, he could also stake a claim to be one of the greatest social media users in sports. \n\n\"I think they're both playing their roles,\" says <PERSON><PERSON><PERSON>, who promotes over 45 boxers. \"You've got the bad guy and the good guy, really. You've got the guy who throws the money around (<PERSON><PERSON><PERSON>), that's his image, and <PERSON><PERSON><PERSON>, he's the hope of a nation.\" ", "questions": ["Who are the two boxer featured in this article?", "What is <PERSON><PERSON><PERSON><PERSON> nick name?", "what is the other", "which stand for?", "what is the name of his clothing line?", "Who is <PERSON><PERSON><PERSON>?", "how many people does he promote", "what is the combined purse for this match?", "how old is mayweather?", "what do they say about <PERSON><PERSON><PERSON><PERSON> and his following?"], "answers": {"input_text": ["<PERSON> and <PERSON>", "1 is the money man", "TBE", "The Best Ever", "The Money Team,", "a boxing promoter", "over 45 boxers.", "$300 million pending viewership numbers", "38", "just that it has bible references and shows him enjoying gos[e; singing with his wife"], "answer_start": [330, 851, 851, 945, 1175, 753, 1472, 504, 851, 6], "answer_end": [383, 1085, 971, 970, 1253, 791, 1511, 671, 884, 132]}}}, {"input": {"input": "What is the main topic?", "output": "OCLC", "expected": "OCLC"}, "expected": 1, "metadata": {"source": "wikipedia", "story": "OCLC, currently incorporated as OCLC Online Computer Library Center, Incorporated, is an American nonprofit cooperative organization \"dedicated to the public purposes of furthering access to the world's information and reducing information costs\". It was founded in 1967 as the Ohio College Library Center. OCLC and its member libraries cooperatively produce and maintain WorldCat, the largest online public access catalog (OPAC) in the world. OCLC is funded mainly by the fees that libraries have to pay for its services (around $200 million annually ). \n\nOCLC began in 1967, as the Ohio College Library Center, through a collaboration of Ohio university presidents, vice presidents, and library directors who wanted to create a cooperative, computerized network for Ohio libraries. The group first met on July 5, 1967 on the campus of the Ohio State University to sign the articles of incorporation for the nonprofit organization. The group hired <PERSON>, a former Yale University medical school librarian, to design the shared cataloging system. <PERSON><PERSON><PERSON><PERSON> wished to merge the latest information storage and retrieval system of the time, the computer, with the oldest, the library. The plan was to merge the catalogs of Ohio libraries electronically through a computer network and database to streamline operations, control costs, and increase efficiency in library management. The goal of this network and database was to bring libraries together to cooperatively keep track of the world's information to best serve researchers and scholars. The first library to do online cataloging through OCLC was the Alden Library at Ohio University on August 26, 1971. This was the first occurrence of online cataloging by any library worldwide.", "questions": ["What is the main topic?", "What does it stand for?", "When did it begin?", "Was it founded the same year?", "Where?", "What location there?", "What Yale student was hired by the organization?", "Was he currently enrolled at the University?", "What was his profession while there?", "What does OCLC produce and maintain?", "When did the group first gather?", "Where?", "Which was the first online library through them?", "Where?", "What could the site do?", "On what date did this happen?", "Had this been done before?"], "answers": {"input_text": ["OCLC", "Online Computer Library Center", "1967", "Yes", "Ohio", "Ohio State University", "<PERSON>", "He is not", "medical school librarian", "WorldCat", "July 5, 1967", "Ohio State University", "Alden Library", "Ohio University", "online cataloging", "August 26, 1971", "no"], "answer_start": [32, 37, 571, 806, 278, 841, 949, 971, 995, 372, 806, 841, 1618, 1636, 1579, 1654, 1671], "answer_end": [36, 67, 575, 819, 282, 862, 969, 1020, 1020, 380, 820, 863, 1632, 1651, 1597, 1670, 1746]}}}, {"input": {"input": "What is the main topic?", "output": "Online Computer Library Center", "expected": "OCLC"}, "expected": 0, "metadata": {"source": "wikipedia", "story": "OCLC, currently incorporated as OCLC Online Computer Library Center, Incorporated, is an American nonprofit cooperative organization \"dedicated to the public purposes of furthering access to the world's information and reducing information costs\". It was founded in 1967 as the Ohio College Library Center. OCLC and its member libraries cooperatively produce and maintain WorldCat, the largest online public access catalog (OPAC) in the world. OCLC is funded mainly by the fees that libraries have to pay for its services (around $200 million annually ). \n\nOCLC began in 1967, as the Ohio College Library Center, through a collaboration of Ohio university presidents, vice presidents, and library directors who wanted to create a cooperative, computerized network for Ohio libraries. The group first met on July 5, 1967 on the campus of the Ohio State University to sign the articles of incorporation for the nonprofit organization. The group hired <PERSON>, a former Yale University medical school librarian, to design the shared cataloging system. <PERSON><PERSON><PERSON><PERSON> wished to merge the latest information storage and retrieval system of the time, the computer, with the oldest, the library. The plan was to merge the catalogs of Ohio libraries electronically through a computer network and database to streamline operations, control costs, and increase efficiency in library management. The goal of this network and database was to bring libraries together to cooperatively keep track of the world's information to best serve researchers and scholars. The first library to do online cataloging through OCLC was the Alden Library at Ohio University on August 26, 1971. This was the first occurrence of online cataloging by any library worldwide.", "questions": ["What is the main topic?", "What does it stand for?", "When did it begin?", "Was it founded the same year?", "Where?", "What location there?", "What Yale student was hired by the organization?", "Was he currently enrolled at the University?", "What was his profession while there?", "What does OCLC produce and maintain?", "When did the group first gather?", "Where?", "Which was the first online library through them?", "Where?", "What could the site do?", "On what date did this happen?", "Had this been done before?"], "answers": {"input_text": ["OCLC", "Online Computer Library Center", "1967", "Yes", "Ohio", "Ohio State University", "<PERSON>", "He is not", "medical school librarian", "WorldCat", "July 5, 1967", "Ohio State University", "Alden Library", "Ohio University", "online cataloging", "August 26, 1971", "no"], "answer_start": [32, 37, 571, 806, 278, 841, 949, 971, 995, 372, 806, 841, 1618, 1636, 1579, 1654, 1671], "answer_end": [36, 67, 575, 819, 282, 862, 969, 1020, 1020, 380, 820, 863, 1632, 1651, 1597, 1670, 1746]}}}, {"input": {"input": "What is the main topic?", "output": "Online Computer Library Center OCLC 1967", "expected": "OCLC"}, "expected": 0.6, "metadata": {"source": "wikipedia", "story": "OCLC, currently incorporated as OCLC Online Computer Library Center, Incorporated, is an American nonprofit cooperative organization \"dedicated to the public purposes of furthering access to the world's information and reducing information costs\". It was founded in 1967 as the Ohio College Library Center. OCLC and its member libraries cooperatively produce and maintain WorldCat, the largest online public access catalog (OPAC) in the world. OCLC is funded mainly by the fees that libraries have to pay for its services (around $200 million annually ). \n\nOCLC began in 1967, as the Ohio College Library Center, through a collaboration of Ohio university presidents, vice presidents, and library directors who wanted to create a cooperative, computerized network for Ohio libraries. The group first met on July 5, 1967 on the campus of the Ohio State University to sign the articles of incorporation for the nonprofit organization. The group hired <PERSON>, a former Yale University medical school librarian, to design the shared cataloging system. <PERSON><PERSON><PERSON><PERSON> wished to merge the latest information storage and retrieval system of the time, the computer, with the oldest, the library. The plan was to merge the catalogs of Ohio libraries electronically through a computer network and database to streamline operations, control costs, and increase efficiency in library management. The goal of this network and database was to bring libraries together to cooperatively keep track of the world's information to best serve researchers and scholars. The first library to do online cataloging through OCLC was the Alden Library at Ohio University on August 26, 1971. This was the first occurrence of online cataloging by any library worldwide.", "questions": ["What is the main topic?", "What does it stand for?", "When did it begin?", "Was it founded the same year?", "Where?", "What location there?", "What Yale student was hired by the organization?", "Was he currently enrolled at the University?", "What was his profession while there?", "What does OCLC produce and maintain?", "When did the group first gather?", "Where?", "Which was the first online library through them?", "Where?", "What could the site do?", "On what date did this happen?", "Had this been done before?"], "answers": {"input_text": ["OCLC", "Online Computer Library Center", "1967", "Yes", "Ohio", "Ohio State University", "<PERSON>", "He is not", "medical school librarian", "WorldCat", "July 5, 1967", "Ohio State University", "Alden Library", "Ohio University", "online cataloging", "August 26, 1971", "no"], "answer_start": [32, 37, 571, 806, 278, 841, 949, 971, 995, 372, 806, 841, 1618, 1636, 1579, 1654, 1671], "answer_end": [36, 67, 575, 819, 282, 862, 969, 1020, 1020, 380, 820, 863, 1632, 1651, 1597, 1670, 1746]}}}, {"input": {"input": "Did they go inside the shop?", "output": "No", "expected": "No"}, "expected": 1, "metadata": {"source": "race", "story": "<PERSON>: Here's a good shop. Shall we buy mother's birthday present here? <PERSON>: Yes, that's a good idea. Shall we go inside? Tom: No. Let's look in the window. Shall we buy her a sweater? <PERSON>: Er, no. It'll soon be summer. Let's buy her a blouse to wear. There's a nice one in the window. <PERSON>: No, she has two blouses. Let's buy a ring. <PERSON>: Oh, no! They're diamond rings. Look at the price. The cheapest is $15. <PERSON>: A real diamond ring is at least $500.They only look like diamonds. Tom: Shall we buy a table? It's only $15. <PERSON>: It doesn't look good, just like a big box. Mum likes chairs. <PERSON>: But they haven't any here. <PERSON>: What about a pen? So cheap! Only $10. <PERSON>: She has a lot of pens and pencils. All of them are new. <PERSON>: Oh, look here. These flowers are beautiful. <PERSON>: They aren't real and will never die. <PERSON>: And they're the cheapest of all these things. Yes, let's buy them. <PERSON>: All right.", "questions": ["Did they go inside the shop?", "What did they end up buying?", "How much was a table?", "Did the purchase it?", "Why not?", "What season will it be soon?", "How much was the cheap diamond?", "Was it a real set?", "What was $10?", "Why didn't the children purchase her a nice shirt?", "What was the occasion for buying a gift?", "What is the cost of a real diamond?"], "answers": {"input_text": ["No", "They bought flowers.", "It's $15.", "No", "It doesn't look good.", "summer", "$15", "No", "a pen", "She already has two blouses", "Mother's birthday", "at least $500"], "answer_start": [127, 759, 523, 534, 534, 213, 408, 456, 644, 297, 48, 442], "answer_end": [129, 766, 526, 555, 554, 220, 411, 484, 649, 316, 56, 455]}}}, {"input": {"input": "Did they go inside the shop?", "output": "They bought flowers.", "expected": "No"}, "expected": 0, "metadata": {"source": "race", "story": "<PERSON>: Here's a good shop. Shall we buy mother's birthday present here? <PERSON>: Yes, that's a good idea. Shall we go inside? Tom: No. Let's look in the window. Shall we buy her a sweater? <PERSON>: Er, no. It'll soon be summer. Let's buy her a blouse to wear. There's a nice one in the window. <PERSON>: No, she has two blouses. Let's buy a ring. <PERSON>: Oh, no! They're diamond rings. Look at the price. The cheapest is $15. <PERSON>: A real diamond ring is at least $500.They only look like diamonds. Tom: Shall we buy a table? It's only $15. <PERSON>: It doesn't look good, just like a big box. Mum likes chairs. <PERSON>: But they haven't any here. <PERSON>: What about a pen? So cheap! Only $10. <PERSON>: She has a lot of pens and pencils. All of them are new. <PERSON>: Oh, look here. These flowers are beautiful. <PERSON>: They aren't real and will never die. <PERSON>: And they're the cheapest of all these things. Yes, let's buy them. <PERSON>: All right.", "questions": ["Did they go inside the shop?", "What did they end up buying?", "How much was a table?", "Did the purchase it?", "Why not?", "What season will it be soon?", "How much was the cheap diamond?", "Was it a real set?", "What was $10?", "Why didn't the children purchase her a nice shirt?", "What was the occasion for buying a gift?", "What is the cost of a real diamond?"], "answers": {"input_text": ["No", "They bought flowers.", "It's $15.", "No", "It doesn't look good.", "summer", "$15", "No", "a pen", "She already has two blouses", "Mother's birthday", "at least $500"], "answer_start": [127, 759, 523, 534, 534, 213, 408, 456, 644, 297, 48, 442], "answer_end": [129, 766, 526, 555, 554, 220, 411, 484, 649, 316, 56, 455]}}}, {"input": {"input": "Did they go inside the shop?", "output": "They bought flowers. No It's $15.", "expected": "No"}, "expected": 0.6, "metadata": {"source": "race", "story": "<PERSON>: Here's a good shop. Shall we buy mother's birthday present here? <PERSON>: Yes, that's a good idea. Shall we go inside? Tom: No. Let's look in the window. Shall we buy her a sweater? <PERSON>: Er, no. It'll soon be summer. Let's buy her a blouse to wear. There's a nice one in the window. <PERSON>: No, she has two blouses. Let's buy a ring. <PERSON>: Oh, no! They're diamond rings. Look at the price. The cheapest is $15. <PERSON>: A real diamond ring is at least $500.They only look like diamonds. Tom: Shall we buy a table? It's only $15. <PERSON>: It doesn't look good, just like a big box. Mum likes chairs. <PERSON>: But they haven't any here. <PERSON>: What about a pen? So cheap! Only $10. <PERSON>: She has a lot of pens and pencils. All of them are new. <PERSON>: Oh, look here. These flowers are beautiful. <PERSON>: They aren't real and will never die. <PERSON>: And they're the cheapest of all these things. Yes, let's buy them. <PERSON>: All right.", "questions": ["Did they go inside the shop?", "What did they end up buying?", "How much was a table?", "Did the purchase it?", "Why not?", "What season will it be soon?", "How much was the cheap diamond?", "Was it a real set?", "What was $10?", "Why didn't the children purchase her a nice shirt?", "What was the occasion for buying a gift?", "What is the cost of a real diamond?"], "answers": {"input_text": ["No", "They bought flowers.", "It's $15.", "No", "It doesn't look good.", "summer", "$15", "No", "a pen", "She already has two blouses", "Mother's birthday", "at least $500"], "answer_start": [127, 759, 523, 534, 534, 213, 408, 456, 644, 297, 48, 442], "answer_end": [129, 766, 526, 555, 554, 220, 411, 484, 649, 316, 56, 455]}}}, {"input": {"input": "Where'd <PERSON> live?", "output": "by a big lake by the woods", "expected": "by a big lake by the woods"}, "expected": 1, "metadata": {"source": "mctest", "story": "<PERSON> was a little boy who lived by a big lake by the woods. One day he saw two little mice looking at the water. They seemed very sad. \n\n\"What is wrong?\" <PERSON> asked. \n\n<PERSON>, one of the little mice, stood. \"My brother and I love to see new things. We want to see what is on the other side of the lake. But we do not know how to build a boat to get there.\" \n\n<PERSON> smiled. \"I have plenty of toy boats,\" he said. \"They are your size!\" \n\n\"Oh, thank you!\" said <PERSON>, the other little mouse. \"We would be very glad if we could borrow it!\" \n\n<PERSON> went to his house and returned with a small toy boat. The boat had a big sail to make the boat go. <PERSON> and <PERSON> climbed on and <PERSON> put the boat into the water. \n\nThe boat sailed away across the water! <PERSON> and <PERSON> laughed with joy and had great fun riding the toy boat. \n\nSoon, they reached the other side of the lake. Many people were having fun swimming and splashing in the water. <PERSON> and <PERSON> loved seeing all the new things and people. \n\nA man threw a ball into the water. It landed by the toy boat. The man's big dog came splashing into the water to fetch it. The big waves splashed the little boat every which way! <PERSON> and <PERSON> got very wet. \n\n<PERSON> and <PERSON> went back home after that. They told <PERSON> of what they saw, and they all laughed at the big dog's wave. Tomorrow the mice would see even more of the lake on <PERSON>'s toy boat!", "questions": ["Where'd <PERSON> live?", "what'd he see?", "What does <PERSON> have?", "Did the mice have names?", "what were they?", "where'd he get the boats?", "What's the mice do?", "What were people doing?", "What did the man do?", "What did the do do?"], "answers": {"input_text": ["by a big lake by the woods", "mice", "toy boats", "yes", "<PERSON> and <PERSON>", "his house", "climbed on", "swimming and splashing", "threw a ball into the water", "got very wet"], "answer_start": [33, 76, 381, 170, 749, 552, 658, 897, 1001, 1189], "answer_end": [59, 91, 400, 198, 763, 561, 668, 933, 1028, 1201]}}}, {"input": {"input": "Where'd <PERSON> live?", "output": "mice", "expected": "by a big lake by the woods"}, "expected": 0, "metadata": {"source": "mctest", "story": "<PERSON> was a little boy who lived by a big lake by the woods. One day he saw two little mice looking at the water. They seemed very sad. \n\n\"What is wrong?\" <PERSON> asked. \n\n<PERSON>, one of the little mice, stood. \"My brother and I love to see new things. We want to see what is on the other side of the lake. But we do not know how to build a boat to get there.\" \n\n<PERSON> smiled. \"I have plenty of toy boats,\" he said. \"They are your size!\" \n\n\"Oh, thank you!\" said <PERSON>, the other little mouse. \"We would be very glad if we could borrow it!\" \n\n<PERSON> went to his house and returned with a small toy boat. The boat had a big sail to make the boat go. <PERSON> and <PERSON> climbed on and <PERSON> put the boat into the water. \n\nThe boat sailed away across the water! <PERSON> and <PERSON> laughed with joy and had great fun riding the toy boat. \n\nSoon, they reached the other side of the lake. Many people were having fun swimming and splashing in the water. <PERSON> and <PERSON> loved seeing all the new things and people. \n\nA man threw a ball into the water. It landed by the toy boat. The man's big dog came splashing into the water to fetch it. The big waves splashed the little boat every which way! <PERSON> and <PERSON> got very wet. \n\n<PERSON> and <PERSON> went back home after that. They told <PERSON> of what they saw, and they all laughed at the big dog's wave. Tomorrow the mice would see even more of the lake on <PERSON>'s toy boat!", "questions": ["Where'd <PERSON> live?", "what'd he see?", "What does <PERSON> have?", "Did the mice have names?", "what were they?", "where'd he get the boats?", "What's the mice do?", "What were people doing?", "What did the man do?", "What did the do do?"], "answers": {"input_text": ["by a big lake by the woods", "mice", "toy boats", "yes", "<PERSON> and <PERSON>", "his house", "climbed on", "swimming and splashing", "threw a ball into the water", "got very wet"], "answer_start": [33, 76, 381, 170, 749, 552, 658, 897, 1001, 1189], "answer_end": [59, 91, 400, 198, 763, 561, 668, 933, 1028, 1201]}}}, {"input": {"input": "Where'd <PERSON> live?", "output": "mice by a big lake by the woods toy boats", "expected": "by a big lake by the woods"}, "expected": 0.6, "metadata": {"source": "mctest", "story": "<PERSON> was a little boy who lived by a big lake by the woods. One day he saw two little mice looking at the water. They seemed very sad. \n\n\"What is wrong?\" <PERSON> asked. \n\n<PERSON>, one of the little mice, stood. \"My brother and I love to see new things. We want to see what is on the other side of the lake. But we do not know how to build a boat to get there.\" \n\n<PERSON> smiled. \"I have plenty of toy boats,\" he said. \"They are your size!\" \n\n\"Oh, thank you!\" said <PERSON>, the other little mouse. \"We would be very glad if we could borrow it!\" \n\n<PERSON> went to his house and returned with a small toy boat. The boat had a big sail to make the boat go. <PERSON> and <PERSON> climbed on and <PERSON> put the boat into the water. \n\nThe boat sailed away across the water! <PERSON> and <PERSON> laughed with joy and had great fun riding the toy boat. \n\nSoon, they reached the other side of the lake. Many people were having fun swimming and splashing in the water. <PERSON> and <PERSON> loved seeing all the new things and people. \n\nA man threw a ball into the water. It landed by the toy boat. The man's big dog came splashing into the water to fetch it. The big waves splashed the little boat every which way! <PERSON> and <PERSON> got very wet. \n\n<PERSON> and <PERSON> went back home after that. They told <PERSON> of what they saw, and they all laughed at the big dog's wave. Tomorrow the mice would see even more of the lake on <PERSON>'s toy boat!", "questions": ["Where'd <PERSON> live?", "what'd he see?", "What does <PERSON> have?", "Did the mice have names?", "what were they?", "where'd he get the boats?", "What's the mice do?", "What were people doing?", "What did the man do?", "What did the do do?"], "answers": {"input_text": ["by a big lake by the woods", "mice", "toy boats", "yes", "<PERSON> and <PERSON>", "his house", "climbed on", "swimming and splashing", "threw a ball into the water", "got very wet"], "answer_start": [33, 76, 381, 170, 749, 552, 658, 897, 1001, 1189], "answer_end": [59, 91, 400, 198, 763, 561, 668, 933, 1028, 1201]}}}, {"input": {"input": "What happened to the hospital where <PERSON> was?", "output": "The hospital had been bombed.", "expected": "The hospital had been bombed."}, "expected": 1, "metadata": {"source": "race", "story": "A German taxi-driver, <PERSON>, recently found his brother who was thought to have been killed twenty years ago. \n\nWhile on a walking tour with his wife, he stopped to talk to a workman. After they had gone on, Mrs. <PERSON> said that the workman was closely like her husband and even suggested that he might be his brother. <PERSON> laughed at the idea, pointing out that his brother had been killed in action during the war. Though Mrs. <PERSON> knew this story quite well, she thought there was a chance in a million that she might be right. \n\nA few days later, she sent a boy to the workman to ask him if his name was <PERSON>. Needless to say, the man's name was <PERSON>. And he really was <PERSON>'s long-lost brother. \n\nWhen the brothers were reunited, <PERSON> explained how it was that he was still alive. \n\nAfter having been wondered towards the end of the war, he had been sent to hospital and was separated from his unit . The hospital had been bombed and <PERSON> had made his way back into Western Germany on foot. Meanwhile, his unit was lost and all records of him had been destroyed. <PERSON> returned to his home, but the house had been bombed up. Guessing that his family had all been killed during an air-raid , <PERSON> settled down in a village fifty miles away where he had remained ever since.", "questions": ["What happened to the hospital where <PERSON> was?", "Was he with his unit at the time?", "What country was he in?", "Was he in the Eastern or Western part?", "And which part did he need to get to?", "Did he drive there?", "Was the war nearly over by this time?", "Was his house still standing?", "Did he know what happened to his family?", "What did he decide to do?", "Did he stay there long?", "What kind of job did he do?", "Does he have a living brother?", "What's his name?", "Does he drive a car for a living?", "Did the two brothers keep in contact the past twenty years?", "Why didn't <PERSON> stay in touch?", "Who noticed the resemblance between the two men?", "What did <PERSON> think of that?", "Did she give up at that point?"], "answers": {"input_text": ["The hospital had been bombed.", "No.", "Germany", "Eastern Germany at the time of his hospital stay.", "Western Germany", "No", "Yes", "No", "No, just guessed.", "<PERSON> settled down in a village fifty miles away.", "Yes, for twenty years", "a workman", "Yes", "<PERSON>", "Yes, <PERSON> does.", "No", "He assumed <PERSON> was dead.", "Mrs. <PERSON>sman", "<PERSON> laughed at the idea", "No"], "answer_start": [936, 906, 2, 969, 1001, 1017, 845, 1129, 1159, 1225, 100, 181, 689, 671, 2, 732, 373, 215, 329, 546], "answer_end": [964, 933, 9, 1016, 1016, 1024, 870, 1157, 1222, 1267, 112, 190, 728, 683, 35, 763, 474, 280, 355, 634]}}}, {"input": {"input": "What happened to the hospital where <PERSON> was?", "output": "No.", "expected": "The hospital had been bombed."}, "expected": 0, "metadata": {"source": "race", "story": "A German taxi-driver, <PERSON>, recently found his brother who was thought to have been killed twenty years ago. \n\nWhile on a walking tour with his wife, he stopped to talk to a workman. After they had gone on, Mrs. <PERSON> said that the workman was closely like her husband and even suggested that he might be his brother. <PERSON> laughed at the idea, pointing out that his brother had been killed in action during the war. Though Mrs. <PERSON> knew this story quite well, she thought there was a chance in a million that she might be right. \n\nA few days later, she sent a boy to the workman to ask him if his name was <PERSON>. Needless to say, the man's name was <PERSON>. And he really was <PERSON>'s long-lost brother. \n\nWhen the brothers were reunited, <PERSON> explained how it was that he was still alive. \n\nAfter having been wondered towards the end of the war, he had been sent to hospital and was separated from his unit . The hospital had been bombed and <PERSON> had made his way back into Western Germany on foot. Meanwhile, his unit was lost and all records of him had been destroyed. <PERSON> returned to his home, but the house had been bombed up. Guessing that his family had all been killed during an air-raid , <PERSON> settled down in a village fifty miles away where he had remained ever since.", "questions": ["What happened to the hospital where <PERSON> was?", "Was he with his unit at the time?", "What country was he in?", "Was he in the Eastern or Western part?", "And which part did he need to get to?", "Did he drive there?", "Was the war nearly over by this time?", "Was his house still standing?", "Did he know what happened to his family?", "What did he decide to do?", "Did he stay there long?", "What kind of job did he do?", "Does he have a living brother?", "What's his name?", "Does he drive a car for a living?", "Did the two brothers keep in contact the past twenty years?", "Why didn't <PERSON> stay in touch?", "Who noticed the resemblance between the two men?", "What did <PERSON> think of that?", "Did she give up at that point?"], "answers": {"input_text": ["The hospital had been bombed.", "No.", "Germany", "Eastern Germany at the time of his hospital stay.", "Western Germany", "No", "Yes", "No", "No, just guessed.", "<PERSON> settled down in a village fifty miles away.", "Yes, for twenty years", "a workman", "Yes", "<PERSON>", "Yes, <PERSON> does.", "No", "He assumed <PERSON> was dead.", "Mrs. <PERSON>sman", "<PERSON> laughed at the idea", "No"], "answer_start": [936, 906, 2, 969, 1001, 1017, 845, 1129, 1159, 1225, 100, 181, 689, 671, 2, 732, 373, 215, 329, 546], "answer_end": [964, 933, 9, 1016, 1016, 1024, 870, 1157, 1222, 1267, 112, 190, 728, 683, 35, 763, 474, 280, 355, 634]}}}, {"input": {"input": "What happened to the hospital where <PERSON> was?", "output": "No. The hospital had been bombed. Germany", "expected": "The hospital had been bombed."}, "expected": 0.6, "metadata": {"source": "race", "story": "A German taxi-driver, <PERSON>, recently found his brother who was thought to have been killed twenty years ago. \n\nWhile on a walking tour with his wife, he stopped to talk to a workman. After they had gone on, Mrs. <PERSON> said that the workman was closely like her husband and even suggested that he might be his brother. <PERSON> laughed at the idea, pointing out that his brother had been killed in action during the war. Though Mrs. <PERSON> knew this story quite well, she thought there was a chance in a million that she might be right. \n\nA few days later, she sent a boy to the workman to ask him if his name was <PERSON>. Needless to say, the man's name was <PERSON>. And he really was <PERSON>'s long-lost brother. \n\nWhen the brothers were reunited, <PERSON> explained how it was that he was still alive. \n\nAfter having been wondered towards the end of the war, he had been sent to hospital and was separated from his unit . The hospital had been bombed and <PERSON> had made his way back into Western Germany on foot. Meanwhile, his unit was lost and all records of him had been destroyed. <PERSON> returned to his home, but the house had been bombed up. Guessing that his family had all been killed during an air-raid , <PERSON> settled down in a village fifty miles away where he had remained ever since.", "questions": ["What happened to the hospital where <PERSON> was?", "Was he with his unit at the time?", "What country was he in?", "Was he in the Eastern or Western part?", "And which part did he need to get to?", "Did he drive there?", "Was the war nearly over by this time?", "Was his house still standing?", "Did he know what happened to his family?", "What did he decide to do?", "Did he stay there long?", "What kind of job did he do?", "Does he have a living brother?", "What's his name?", "Does he drive a car for a living?", "Did the two brothers keep in contact the past twenty years?", "Why didn't <PERSON> stay in touch?", "Who noticed the resemblance between the two men?", "What did <PERSON> think of that?", "Did she give up at that point?"], "answers": {"input_text": ["The hospital had been bombed.", "No.", "Germany", "Eastern Germany at the time of his hospital stay.", "Western Germany", "No", "Yes", "No", "No, just guessed.", "<PERSON> settled down in a village fifty miles away.", "Yes, for twenty years", "a workman", "Yes", "<PERSON>", "Yes, <PERSON> does.", "No", "He assumed <PERSON> was dead.", "Mrs. <PERSON>sman", "<PERSON> laughed at the idea", "No"], "answer_start": [936, 906, 2, 969, 1001, 1017, 845, 1129, 1159, 1225, 100, 181, 689, 671, 2, 732, 373, 215, 329, 546], "answer_end": [964, 933, 9, 1016, 1016, 1024, 870, 1157, 1222, 1267, 112, 190, 728, 683, 35, 763, 474, 280, 355, 634]}}}, {"input": {"input": "What worked her way northward?", "output": "The _<PERSON>_", "expected": "The _<PERSON>_"}, "expected": 1, "metadata": {"source": "<PERSON><PERSON>", "story": "CHAPTER XXII \n\nNorthward, along the leeward coast of Malaita, the _<PERSON>_ worked her leisurely way, threading the colour-riotous lagoon that lay between the shore-reefs and outer-reefs, daring passages so narrow and coral-patched that Captain <PERSON> averred each day added a thousand grey hairs to his head, and dropping anchor off every walled inlet of the outer reef and every mangrove swamp of the mainland that looked promising of cannibal life. For <PERSON> and <PERSON> were in no hurry. So long as the way was interesting, they dared not how long it proved from anywhere to anywhere. \n\nDuring this time <PERSON> learned a new name for himself--or, rather, an entire series of names for himself. This was because of an aversion on <PERSON>'s part against renaming a named thing. \n\n\"A name he must have had,\" he argued to <PERSON>. \"<PERSON><PERSON><PERSON> must have named him before he sailed on the _Arangi_. Therefore, nameless he must be until we get back to Tulagi and find out his real name.\" \n\n\"What's in a name?\" <PERSON> had begun to tease. \n\n\"Everything,\" her husband retorted. \"Think of yourself, shipwrecked, called by your rescuers 'Mrs. <PERSON><PERSON>,' or '<PERSON><PERSON><PERSON>,' or just plain '<PERSON><PERSON>.' And think of me being called '<PERSON>,' or ' <PERSON><PERSON>,' or . . . or . . . '<PERSON><PERSON>.' No, keep him nameless, until we find out his original name.\" \n\n\"Must call him something,\" she objected. \"Can't think of him without thinking something.\" \n\n\"Then call him many names, but never the same name twice. Call him '<PERSON>' to-day, and 'Mister <PERSON>' to-morrow, and the next day something else.\" ", "questions": ["What worked her way northward?", "What lay between the shore-reefs and outer-reefs?", "Were the passages wide?", "Who was the Captain?", "Did he have red hair?", "Were <PERSON> and <PERSON> in a hurry?", "What coast did the <PERSON> work her way up leisurely?", "Who argued to <PERSON> that he must have a name?", "Who began to tease <PERSON>?", "What ship had <PERSON>gg<PERSON> sailed on?", "How long must he be nameless until?", "Who was the husband she retorted something to?", "Did she tell him to think of himself being in an airplane crash?", "What would an awkward name to be called by rescuers be?", "What's another one?", "How about a third one?", "Did she ask <PERSON> to think of her being called <PERSON>?", "Was there a lot of coral in the lagoon?", "Where did the Ariel drop anchor off of?", "Were there cannibals?"], "answers": {"input_text": ["The _<PERSON>_", "Lagoon", "No", "Winters", "No", "No", "Malaita", "<PERSON>", "Villa", "The Arangi", "Until they get back to Tulagi", "<PERSON>", "No", "'Mrs. <PERSON>'", "'Topsy'", "'<PERSON><PERSON><PERSON>'", "No", "Yes", "Every walled inlet of the outer reef and every mangrove swamp of the mainland that looked promising of cannibal life.", "unknown"], "answer_start": [15, 100, 186, 234, 235, 451, 15, 702, 992, 839, 901, 711, 1, 1039, 1189, 1149, 1039, 15, 312, -1], "answer_end": [98, 184, 212, 250, 295, 497, 83, 882, 1037, 900, 988, 1074, 1583, 1348, 1197, 1173, 1348, 229, 450, -1]}}}, {"input": {"input": "What worked her way northward?", "output": "Lagoon", "expected": "The _<PERSON>_"}, "expected": 0, "metadata": {"source": "<PERSON><PERSON>", "story": "CHAPTER XXII \n\nNorthward, along the leeward coast of Malaita, the _<PERSON>_ worked her leisurely way, threading the colour-riotous lagoon that lay between the shore-reefs and outer-reefs, daring passages so narrow and coral-patched that Captain <PERSON> averred each day added a thousand grey hairs to his head, and dropping anchor off every walled inlet of the outer reef and every mangrove swamp of the mainland that looked promising of cannibal life. For <PERSON> and <PERSON> were in no hurry. So long as the way was interesting, they dared not how long it proved from anywhere to anywhere. \n\nDuring this time <PERSON> learned a new name for himself--or, rather, an entire series of names for himself. This was because of an aversion on <PERSON>'s part against renaming a named thing. \n\n\"A name he must have had,\" he argued to <PERSON>. \"<PERSON><PERSON><PERSON> must have named him before he sailed on the _Arangi_. Therefore, nameless he must be until we get back to Tulagi and find out his real name.\" \n\n\"What's in a name?\" <PERSON> had begun to tease. \n\n\"Everything,\" her husband retorted. \"Think of yourself, shipwrecked, called by your rescuers 'Mrs. <PERSON><PERSON>,' or '<PERSON><PERSON><PERSON>,' or just plain '<PERSON><PERSON>.' And think of me being called '<PERSON>,' or ' <PERSON><PERSON>,' or . . . or . . . '<PERSON><PERSON>.' No, keep him nameless, until we find out his original name.\" \n\n\"Must call him something,\" she objected. \"Can't think of him without thinking something.\" \n\n\"Then call him many names, but never the same name twice. Call him '<PERSON>' to-day, and 'Mister <PERSON>' to-morrow, and the next day something else.\" ", "questions": ["What worked her way northward?", "What lay between the shore-reefs and outer-reefs?", "Were the passages wide?", "Who was the Captain?", "Did he have red hair?", "Were <PERSON> and <PERSON> in a hurry?", "What coast did the <PERSON> work her way up leisurely?", "Who argued to <PERSON> that he must have a name?", "Who began to tease <PERSON>?", "What ship had <PERSON>gg<PERSON> sailed on?", "How long must he be nameless until?", "Who was the husband she retorted something to?", "Did she tell him to think of himself being in an airplane crash?", "What would an awkward name to be called by rescuers be?", "What's another one?", "How about a third one?", "Did she ask <PERSON> to think of her being called <PERSON>?", "Was there a lot of coral in the lagoon?", "Where did the Ariel drop anchor off of?", "Were there cannibals?"], "answers": {"input_text": ["The _<PERSON>_", "Lagoon", "No", "Winters", "No", "No", "Malaita", "<PERSON>", "Villa", "The Arangi", "Until they get back to Tulagi", "<PERSON>", "No", "'Mrs. <PERSON>'", "'Topsy'", "'<PERSON><PERSON><PERSON>'", "No", "Yes", "Every walled inlet of the outer reef and every mangrove swamp of the mainland that looked promising of cannibal life.", "unknown"], "answer_start": [15, 100, 186, 234, 235, 451, 15, 702, 992, 839, 901, 711, 1, 1039, 1189, 1149, 1039, 15, 312, -1], "answer_end": [98, 184, 212, 250, 295, 497, 83, 882, 1037, 900, 988, 1074, 1583, 1348, 1197, 1173, 1348, 229, 450, -1]}}}, {"input": {"input": "What worked her way northward?", "output": "Lagoon The _Ariel_ No", "expected": "The _<PERSON>_"}, "expected": 0.6, "metadata": {"source": "<PERSON><PERSON>", "story": "CHAPTER XXII \n\nNorthward, along the leeward coast of Malaita, the _<PERSON>_ worked her leisurely way, threading the colour-riotous lagoon that lay between the shore-reefs and outer-reefs, daring passages so narrow and coral-patched that Captain <PERSON> averred each day added a thousand grey hairs to his head, and dropping anchor off every walled inlet of the outer reef and every mangrove swamp of the mainland that looked promising of cannibal life. For <PERSON> and <PERSON> were in no hurry. So long as the way was interesting, they dared not how long it proved from anywhere to anywhere. \n\nDuring this time <PERSON> learned a new name for himself--or, rather, an entire series of names for himself. This was because of an aversion on <PERSON>'s part against renaming a named thing. \n\n\"A name he must have had,\" he argued to <PERSON>. \"<PERSON><PERSON><PERSON> must have named him before he sailed on the _Arangi_. Therefore, nameless he must be until we get back to Tulagi and find out his real name.\" \n\n\"What's in a name?\" <PERSON> had begun to tease. \n\n\"Everything,\" her husband retorted. \"Think of yourself, shipwrecked, called by your rescuers 'Mrs. <PERSON><PERSON>,' or '<PERSON><PERSON><PERSON>,' or just plain '<PERSON><PERSON>.' And think of me being called '<PERSON>,' or ' <PERSON><PERSON>,' or . . . or . . . '<PERSON><PERSON>.' No, keep him nameless, until we find out his original name.\" \n\n\"Must call him something,\" she objected. \"Can't think of him without thinking something.\" \n\n\"Then call him many names, but never the same name twice. Call him '<PERSON>' to-day, and 'Mister <PERSON>' to-morrow, and the next day something else.\" ", "questions": ["What worked her way northward?", "What lay between the shore-reefs and outer-reefs?", "Were the passages wide?", "Who was the Captain?", "Did he have red hair?", "Were <PERSON> and <PERSON> in a hurry?", "What coast did the <PERSON> work her way up leisurely?", "Who argued to <PERSON> that he must have a name?", "Who began to tease <PERSON>?", "What ship had <PERSON>gg<PERSON> sailed on?", "How long must he be nameless until?", "Who was the husband she retorted something to?", "Did she tell him to think of himself being in an airplane crash?", "What would an awkward name to be called by rescuers be?", "What's another one?", "How about a third one?", "Did she ask <PERSON> to think of her being called <PERSON>?", "Was there a lot of coral in the lagoon?", "Where did the Ariel drop anchor off of?", "Were there cannibals?"], "answers": {"input_text": ["The _<PERSON>_", "Lagoon", "No", "Winters", "No", "No", "Malaita", "<PERSON>", "Villa", "The Arangi", "Until they get back to Tulagi", "<PERSON>", "No", "'Mrs. <PERSON>'", "'Topsy'", "'<PERSON><PERSON><PERSON>'", "No", "Yes", "Every walled inlet of the outer reef and every mangrove swamp of the mainland that looked promising of cannibal life.", "unknown"], "answer_start": [15, 100, 186, 234, 235, 451, 15, 702, 992, 839, 901, 711, 1, 1039, 1189, 1149, 1039, 15, 312, -1], "answer_end": [98, 184, 212, 250, 295, 497, 83, 882, 1037, 900, 988, 1074, 1583, 1348, 1197, 1173, 1348, 229, 450, -1]}}}, {"input": {"input": "Who were the two canines who lived next door to each other?", "output": "<PERSON><PERSON> and Spotty", "expected": "<PERSON><PERSON> and Spotty"}, "expected": 1, "metadata": {"source": "race", "story": "<PERSON><PERSON> and <PERSON><PERSON> were neighbor dogs who met every day to play together. These two loved each other and played together so often that they had worn a path through the grass of the field between their respective houses. One evening, <PERSON><PERSON>'s family noticed that <PERSON><PERSON> hadn't returned home. They went looking for him with no success and by the next week he was still missing. Curiously, <PERSON><PERSON> showed up at <PERSON><PERSON>'s house alone. Barking, whining and generally pestering <PERSON><PERSON>'s human family. Busy with their own lives, they just ignored the nervous little neighbor dog. Continuously,<PERSON>, <PERSON><PERSON>'s owner, was disturbed by the angry, determined little dog. <PERSON><PERSON> followed <PERSON> about, barking insistently, then rushing toward a nearby empty lot and back, as if to say, \"Follow me! It's urgent!\" Eventually, <PERSON> followed <PERSON><PERSON> to a deserted spot half a mile from the house. There <PERSON> found his beloved <PERSON><PERSON> alive, one of his hind legs crushed in a steel leghold trap. Horrified, <PERSON> now wished he'd taken <PERSON><PERSON>'s earlier appeals seriously. Then <PERSON> noticed something quite remarkable. <PERSON><PERSON> had done more than simply led <PERSON><PERSON>'s human owner to his trapped friend. In a circle around the injured dog, <PERSON> found an array of dog food and table scraps which were later identified as the remains of every meal <PERSON><PERSON> had been fed that week! <PERSON><PERSON> had been visiting <PERSON><PERSON> regularly, in the hope of keeping his friend alive by sacrificing his own comfort. <PERSON><PERSON> had evidently stayed with <PERSON><PERSON> to protect him from hunger and other dangers, and keep his spirits up. <PERSON><PERSON>'s leg was treated by a veterinarian and he recovered. For many years thereafter, the two families watched the faithful friends chasing each other down that well worn path between their houses.", "questions": ["Who were the two canines who lived next door to each other?", "How often did they visit each other?", "How did they feel about one another?", "Where did they kill the grass?", "Who did <PERSON><PERSON> belong to?", "Which dog had his leg injured?", "Did <PERSON><PERSON> go missing?", "Did <PERSON><PERSON> start annoying <PERSON>?", "Where did <PERSON><PERSON> lead <PERSON> to?", "What's one thing <PERSON><PERSON> did for <PERSON><PERSON> while he was stuck?", "What else did he do?", "And one more?", "Did <PERSON> regret not paying more attention to <PERSON><PERSON>?", "What's something <PERSON><PERSON> did to try to get his attention?", "Did <PERSON><PERSON> survive his ordeal?", "Were the two able to continue playing together?", "Did <PERSON><PERSON> disappear at night?", "Did his family hunt him?", "Was he home by the end of the next week?", "Why did the family ignore <PERSON><PERSON> when he tried to get their attention?", "What did it seem like <PERSON><PERSON> was trying to tell them?"], "answers": {"input_text": ["<PERSON><PERSON> and Spotty", "every day", "loved each other", "worn a path through the grass of the field", "<PERSON>,", "<PERSON><PERSON>", "yes", "Yes", "a spot half a mile from the house", "brought him food", "protect him from other dangers", "keep his spirits up.", "yes", ". <PERSON><PERSON> followed <PERSON> about, barking insistently,", "Yes", "yes", "No", "yes, They went looking for him with no success", "no", "They were busy with their own lives,", "\"Follow me! It's urgent!\""], "answer_start": [0, 0, 74, 134, 588, 883, 220, 389, 678, 1211, 1462, 1548, 975, 659, 1574, 1636, 220, 292, 293, 497, 758], "answer_end": [37, 75, 101, 219, 609, 973, 292, 495, 876, 1346, 1572, 1573, 1045, 708, 1636, 1774, 292, 336, 377, 575, 797]}}}, {"input": {"input": "Who were the two canines who lived next door to each other?", "output": "every day", "expected": "<PERSON><PERSON> and Spotty"}, "expected": 0, "metadata": {"source": "race", "story": "<PERSON><PERSON> and <PERSON><PERSON> were neighbor dogs who met every day to play together. These two loved each other and played together so often that they had worn a path through the grass of the field between their respective houses. One evening, <PERSON><PERSON>'s family noticed that <PERSON><PERSON> hadn't returned home. They went looking for him with no success and by the next week he was still missing. Curiously, <PERSON><PERSON> showed up at <PERSON><PERSON>'s house alone. Barking, whining and generally pestering <PERSON><PERSON>'s human family. Busy with their own lives, they just ignored the nervous little neighbor dog. Continuously,<PERSON>, <PERSON><PERSON>'s owner, was disturbed by the angry, determined little dog. <PERSON><PERSON> followed <PERSON> about, barking insistently, then rushing toward a nearby empty lot and back, as if to say, \"Follow me! It's urgent!\" Eventually, <PERSON> followed <PERSON><PERSON> to a deserted spot half a mile from the house. There <PERSON> found his beloved <PERSON><PERSON> alive, one of his hind legs crushed in a steel leghold trap. Horrified, <PERSON> now wished he'd taken <PERSON><PERSON>'s earlier appeals seriously. Then <PERSON> noticed something quite remarkable. <PERSON><PERSON> had done more than simply led <PERSON><PERSON>'s human owner to his trapped friend. In a circle around the injured dog, <PERSON> found an array of dog food and table scraps which were later identified as the remains of every meal <PERSON><PERSON> had been fed that week! <PERSON><PERSON> had been visiting <PERSON><PERSON> regularly, in the hope of keeping his friend alive by sacrificing his own comfort. <PERSON><PERSON> had evidently stayed with <PERSON><PERSON> to protect him from hunger and other dangers, and keep his spirits up. <PERSON><PERSON>'s leg was treated by a veterinarian and he recovered. For many years thereafter, the two families watched the faithful friends chasing each other down that well worn path between their houses.", "questions": ["Who were the two canines who lived next door to each other?", "How often did they visit each other?", "How did they feel about one another?", "Where did they kill the grass?", "Who did <PERSON><PERSON> belong to?", "Which dog had his leg injured?", "Did <PERSON><PERSON> go missing?", "Did <PERSON><PERSON> start annoying <PERSON>?", "Where did <PERSON><PERSON> lead <PERSON> to?", "What's one thing <PERSON><PERSON> did for <PERSON><PERSON> while he was stuck?", "What else did he do?", "And one more?", "Did <PERSON> regret not paying more attention to <PERSON><PERSON>?", "What's something <PERSON><PERSON> did to try to get his attention?", "Did <PERSON><PERSON> survive his ordeal?", "Were the two able to continue playing together?", "Did <PERSON><PERSON> disappear at night?", "Did his family hunt him?", "Was he home by the end of the next week?", "Why did the family ignore <PERSON><PERSON> when he tried to get their attention?", "What did it seem like <PERSON><PERSON> was trying to tell them?"], "answers": {"input_text": ["<PERSON><PERSON> and Spotty", "every day", "loved each other", "worn a path through the grass of the field", "<PERSON>,", "<PERSON><PERSON>", "yes", "Yes", "a spot half a mile from the house", "brought him food", "protect him from other dangers", "keep his spirits up.", "yes", ". <PERSON><PERSON> followed <PERSON> about, barking insistently,", "Yes", "yes", "No", "yes, They went looking for him with no success", "no", "They were busy with their own lives,", "\"Follow me! It's urgent!\""], "answer_start": [0, 0, 74, 134, 588, 883, 220, 389, 678, 1211, 1462, 1548, 975, 659, 1574, 1636, 220, 292, 293, 497, 758], "answer_end": [37, 75, 101, 219, 609, 973, 292, 495, 876, 1346, 1572, 1573, 1045, 708, 1636, 1774, 292, 336, 377, 575, 797]}}}, {"input": {"input": "Who were the two canines who lived next door to each other?", "output": "every day <PERSON><PERSON> and <PERSON><PERSON> loved each other", "expected": "<PERSON><PERSON> and Spotty"}, "expected": 0.6, "metadata": {"source": "race", "story": "<PERSON><PERSON> and <PERSON><PERSON> were neighbor dogs who met every day to play together. These two loved each other and played together so often that they had worn a path through the grass of the field between their respective houses. One evening, <PERSON><PERSON>'s family noticed that <PERSON><PERSON> hadn't returned home. They went looking for him with no success and by the next week he was still missing. Curiously, <PERSON><PERSON> showed up at <PERSON><PERSON>'s house alone. Barking, whining and generally pestering <PERSON><PERSON>'s human family. Busy with their own lives, they just ignored the nervous little neighbor dog. Continuously,<PERSON>, <PERSON><PERSON>'s owner, was disturbed by the angry, determined little dog. <PERSON><PERSON> followed <PERSON> about, barking insistently, then rushing toward a nearby empty lot and back, as if to say, \"Follow me! It's urgent!\" Eventually, <PERSON> followed <PERSON><PERSON> to a deserted spot half a mile from the house. There <PERSON> found his beloved <PERSON><PERSON> alive, one of his hind legs crushed in a steel leghold trap. Horrified, <PERSON> now wished he'd taken <PERSON><PERSON>'s earlier appeals seriously. Then <PERSON> noticed something quite remarkable. <PERSON><PERSON> had done more than simply led <PERSON><PERSON>'s human owner to his trapped friend. In a circle around the injured dog, <PERSON> found an array of dog food and table scraps which were later identified as the remains of every meal <PERSON><PERSON> had been fed that week! <PERSON><PERSON> had been visiting <PERSON><PERSON> regularly, in the hope of keeping his friend alive by sacrificing his own comfort. <PERSON><PERSON> had evidently stayed with <PERSON><PERSON> to protect him from hunger and other dangers, and keep his spirits up. <PERSON><PERSON>'s leg was treated by a veterinarian and he recovered. For many years thereafter, the two families watched the faithful friends chasing each other down that well worn path between their houses.", "questions": ["Who were the two canines who lived next door to each other?", "How often did they visit each other?", "How did they feel about one another?", "Where did they kill the grass?", "Who did <PERSON><PERSON> belong to?", "Which dog had his leg injured?", "Did <PERSON><PERSON> go missing?", "Did <PERSON><PERSON> start annoying <PERSON>?", "Where did <PERSON><PERSON> lead <PERSON> to?", "What's one thing <PERSON><PERSON> did for <PERSON><PERSON> while he was stuck?", "What else did he do?", "And one more?", "Did <PERSON> regret not paying more attention to <PERSON><PERSON>?", "What's something <PERSON><PERSON> did to try to get his attention?", "Did <PERSON><PERSON> survive his ordeal?", "Were the two able to continue playing together?", "Did <PERSON><PERSON> disappear at night?", "Did his family hunt him?", "Was he home by the end of the next week?", "Why did the family ignore <PERSON><PERSON> when he tried to get their attention?", "What did it seem like <PERSON><PERSON> was trying to tell them?"], "answers": {"input_text": ["<PERSON><PERSON> and Spotty", "every day", "loved each other", "worn a path through the grass of the field", "<PERSON>,", "<PERSON><PERSON>", "yes", "Yes", "a spot half a mile from the house", "brought him food", "protect him from other dangers", "keep his spirits up.", "yes", ". <PERSON><PERSON> followed <PERSON> about, barking insistently,", "Yes", "yes", "No", "yes, They went looking for him with no success", "no", "They were busy with their own lives,", "\"Follow me! It's urgent!\""], "answer_start": [0, 0, 74, 134, 588, 883, 220, 389, 678, 1211, 1462, 1548, 975, 659, 1574, 1636, 220, 292, 293, 497, 758], "answer_end": [37, 75, 101, 219, 609, 973, 292, 495, 876, 1346, 1572, 1573, 1045, 708, 1636, 1774, 292, 336, 377, 575, 797]}}}, {"input": {"input": "What is the story about?", "output": "A girl and a dog.", "expected": "A girl and a dog."}, "expected": 1, "metadata": {"source": "mctest", "story": "This is the story of a young girl and her dog. The young girl and her dog set out a trip into the woods one day. Upon entering the woods the girl and her dog found that the woods were dark and cold. The girl was a little scared and was thinking of turning back, but yet they went on. The girl's dog was acting very interested in what was in the bushes up ahead. To both the girl and the dog's surprise, there was a small brown bear resting in the bushes. The bear was not surprised and did not seem at all interested in the girl and her dog. The bear looked up at the girl and it was almost as if he was smiling at her. He then rested his head on his bear paws and went back to sleep. The girl and the dog kept walking and finally made it out of the woods. To this day the girl does not know why the bear was so friendly and to this day she has never told anyone about the meeting with the bear in the woods.", "questions": ["What is the story about?", "What were they doing?", "where?", "How did the girl feel?", "How about the dog?", "HOw did he feel?", "in what?", "What was it?", "What did it do?", "Did it notice the two?", "How did the girl and the dog feel?", "How did the bear react?", "What did he do?", "Was he mean?", "Does she know why?", "Who did she tell?", "Was the woods open and light?", "What was it like?", "Was she thinking of turning back?", "did she?"], "answers": {"input_text": ["A girl and a dog.", "Set on on a trip", "the woods", "scared", "He wasn't", "He was interested", "what was in the bushes", "a bear", "rested in the bushes", "Not really", "surprised", "not surprised", "Looked at the girl", "He smiled", "No", "no one", "No", "dark and cold", "yes", "no"], "answer_start": [0, 47, 74, 199, 284, 284, 310, 402, 403, 486, 362, 455, 542, 577, 757, 825, 164, 169, 199, 262], "answer_end": [46, 88, 104, 227, 325, 325, 360, 454, 454, 540, 401, 481, 573, 620, 796, 862, 199, 199, 261, 284]}}}, {"input": {"input": "What is the story about?", "output": "Set on on a trip", "expected": "A girl and a dog."}, "expected": 0, "metadata": {"source": "mctest", "story": "This is the story of a young girl and her dog. The young girl and her dog set out a trip into the woods one day. Upon entering the woods the girl and her dog found that the woods were dark and cold. The girl was a little scared and was thinking of turning back, but yet they went on. The girl's dog was acting very interested in what was in the bushes up ahead. To both the girl and the dog's surprise, there was a small brown bear resting in the bushes. The bear was not surprised and did not seem at all interested in the girl and her dog. The bear looked up at the girl and it was almost as if he was smiling at her. He then rested his head on his bear paws and went back to sleep. The girl and the dog kept walking and finally made it out of the woods. To this day the girl does not know why the bear was so friendly and to this day she has never told anyone about the meeting with the bear in the woods.", "questions": ["What is the story about?", "What were they doing?", "where?", "How did the girl feel?", "How about the dog?", "HOw did he feel?", "in what?", "What was it?", "What did it do?", "Did it notice the two?", "How did the girl and the dog feel?", "How did the bear react?", "What did he do?", "Was he mean?", "Does she know why?", "Who did she tell?", "Was the woods open and light?", "What was it like?", "Was she thinking of turning back?", "did she?"], "answers": {"input_text": ["A girl and a dog.", "Set on on a trip", "the woods", "scared", "He wasn't", "He was interested", "what was in the bushes", "a bear", "rested in the bushes", "Not really", "surprised", "not surprised", "Looked at the girl", "He smiled", "No", "no one", "No", "dark and cold", "yes", "no"], "answer_start": [0, 47, 74, 199, 284, 284, 310, 402, 403, 486, 362, 455, 542, 577, 757, 825, 164, 169, 199, 262], "answer_end": [46, 88, 104, 227, 325, 325, 360, 454, 454, 540, 401, 481, 573, 620, 796, 862, 199, 199, 261, 284]}}}, {"input": {"input": "What is the story about?", "output": "Set on on a trip A girl and a dog. the woods", "expected": "A girl and a dog."}, "expected": 0.6, "metadata": {"source": "mctest", "story": "This is the story of a young girl and her dog. The young girl and her dog set out a trip into the woods one day. Upon entering the woods the girl and her dog found that the woods were dark and cold. The girl was a little scared and was thinking of turning back, but yet they went on. The girl's dog was acting very interested in what was in the bushes up ahead. To both the girl and the dog's surprise, there was a small brown bear resting in the bushes. The bear was not surprised and did not seem at all interested in the girl and her dog. The bear looked up at the girl and it was almost as if he was smiling at her. He then rested his head on his bear paws and went back to sleep. The girl and the dog kept walking and finally made it out of the woods. To this day the girl does not know why the bear was so friendly and to this day she has never told anyone about the meeting with the bear in the woods.", "questions": ["What is the story about?", "What were they doing?", "where?", "How did the girl feel?", "How about the dog?", "HOw did he feel?", "in what?", "What was it?", "What did it do?", "Did it notice the two?", "How did the girl and the dog feel?", "How did the bear react?", "What did he do?", "Was he mean?", "Does she know why?", "Who did she tell?", "Was the woods open and light?", "What was it like?", "Was she thinking of turning back?", "did she?"], "answers": {"input_text": ["A girl and a dog.", "Set on on a trip", "the woods", "scared", "He wasn't", "He was interested", "what was in the bushes", "a bear", "rested in the bushes", "Not really", "surprised", "not surprised", "Looked at the girl", "He smiled", "No", "no one", "No", "dark and cold", "yes", "no"], "answer_start": [0, 47, 74, 199, 284, 284, 310, 402, 403, 486, 362, 455, 542, 577, 757, 825, 164, 169, 199, 262], "answer_end": [46, 88, 104, 227, 325, 325, 360, 454, 454, 540, 401, 481, 573, 620, 796, 862, 199, 199, 261, 284]}}}, {"input": {"input": "What happened Friday?", "output": "10-year-old boy fatally shot his father", "expected": "10-year-old boy fatally shot his father"}, "expected": 1, "metadata": {"source": "race", "story": "H<PERSON><PERSON>TO<PERSON> (AP) --- A 10-year-old boy fatally shot his father Friday, striking him several times as he sat in the front seat of a SUV to pick up the boy from his mother's home for a weekend visit. \n\nThe incident happened about 3pm on a cul-desac in the 1700 block of Cedar Cove Court, said Sgt<PERSON> of the Harris County Sheriff's Department. \n\nAn investigation found that <PERSON>, 41, was shot by his son, who was sitting in the back seat of the man's Toyota 4-Runner, said <PERSON><PERSON> <PERSON>. The shooting took place outside the home of <PERSON><PERSON><PERSON><PERSON>'s ex-wife, where the boy lives with his mother and a 7-year-old brother. \n\n\"We're not certain of anything until we finish our investigation,\" <PERSON> said. \"The information we have at this time is that the 10-year-old did fire the weapon.\" \n\nThe mother and the 7-year-old were inside the house when the shooting occurred, said <PERSON>. \n\n<PERSON> said the gun belonged to the boy's mother. After firing shots through the back seat, the boy exited the back of the vehicle and continued to fire at the car. \n\nThe man died on the way to Memorial Herman Hospital. \n\n<PERSON><PERSON><PERSON><PERSON> was a doctor at the University of Texas Medical Branch. \n\nThe man and woman shared custody of the children. \n\n<PERSON> said the mother and the boy were still in the home talking to investigators Friday night. \n\n<PERSON><PERSON><PERSON><PERSON><PERSON> described the family as being quiet and keeping to themselves. \n\n<PERSON>, 17, was walking in the neighborhood when he heard gunshots. \"We've had a suicide in this neighborhood once, but never anything like this,\" <PERSON> said.", "questions": ["What happened Friday?", "Where was he at?", "At his dad's house?", "Where?", "when?", "What time?", "whose gun was it?", "What did the child do after he shot his dad?", "DId he live?", "Where did he work?", "Where was the mother?", "was anyone with her?", "who?"], "answers": {"input_text": ["10-year-old boy fatally shot his father", "in the front seat of a SUV", "no", "outside the home of <PERSON><PERSON><PERSON><PERSON>'s ex-wife", "Friday", "3pm", "belonged to the boy's mother", "He exited the back of the vehicle and continued to fire at the car", "No", "at the University of Texas Medical Branch", "inside the house", "Yes", "the 7-year-old"], "answer_start": [19, 103, 538, 527, 58, 223, 917, 990, 1065, 1141, 834, 813, 813], "answer_end": [59, 130, 565, 565, 65, 227, 946, 1061, 1078, 1183, 850, 828, 829]}}}, {"input": {"input": "What happened Friday?", "output": "in the front seat of a SUV", "expected": "10-year-old boy fatally shot his father"}, "expected": 0, "metadata": {"source": "race", "story": "H<PERSON><PERSON>TO<PERSON> (AP) --- A 10-year-old boy fatally shot his father Friday, striking him several times as he sat in the front seat of a SUV to pick up the boy from his mother's home for a weekend visit. \n\nThe incident happened about 3pm on a cul-desac in the 1700 block of Cedar Cove Court, said Sgt<PERSON> of the Harris County Sheriff's Department. \n\nAn investigation found that <PERSON>, 41, was shot by his son, who was sitting in the back seat of the man's Toyota 4-Runner, said <PERSON><PERSON> <PERSON>. The shooting took place outside the home of <PERSON><PERSON><PERSON><PERSON>'s ex-wife, where the boy lives with his mother and a 7-year-old brother. \n\n\"We're not certain of anything until we finish our investigation,\" <PERSON> said. \"The information we have at this time is that the 10-year-old did fire the weapon.\" \n\nThe mother and the 7-year-old were inside the house when the shooting occurred, said <PERSON>. \n\n<PERSON> said the gun belonged to the boy's mother. After firing shots through the back seat, the boy exited the back of the vehicle and continued to fire at the car. \n\nThe man died on the way to Memorial Herman Hospital. \n\n<PERSON><PERSON><PERSON><PERSON> was a doctor at the University of Texas Medical Branch. \n\nThe man and woman shared custody of the children. \n\n<PERSON> said the mother and the boy were still in the home talking to investigators Friday night. \n\n<PERSON><PERSON><PERSON><PERSON><PERSON> described the family as being quiet and keeping to themselves. \n\n<PERSON>, 17, was walking in the neighborhood when he heard gunshots. \"We've had a suicide in this neighborhood once, but never anything like this,\" <PERSON> said.", "questions": ["What happened Friday?", "Where was he at?", "At his dad's house?", "Where?", "when?", "What time?", "whose gun was it?", "What did the child do after he shot his dad?", "DId he live?", "Where did he work?", "Where was the mother?", "was anyone with her?", "who?"], "answers": {"input_text": ["10-year-old boy fatally shot his father", "in the front seat of a SUV", "no", "outside the home of <PERSON><PERSON><PERSON><PERSON>'s ex-wife", "Friday", "3pm", "belonged to the boy's mother", "He exited the back of the vehicle and continued to fire at the car", "No", "at the University of Texas Medical Branch", "inside the house", "Yes", "the 7-year-old"], "answer_start": [19, 103, 538, 527, 58, 223, 917, 990, 1065, 1141, 834, 813, 813], "answer_end": [59, 130, 565, 565, 65, 227, 946, 1061, 1078, 1183, 850, 828, 829]}}}, {"input": {"input": "What happened Friday?", "output": "in the front seat of a SUV 10-year-old boy fatally shot his father no", "expected": "10-year-old boy fatally shot his father"}, "expected": 0.6, "metadata": {"source": "race", "story": "H<PERSON><PERSON>TO<PERSON> (AP) --- A 10-year-old boy fatally shot his father Friday, striking him several times as he sat in the front seat of a SUV to pick up the boy from his mother's home for a weekend visit. \n\nThe incident happened about 3pm on a cul-desac in the 1700 block of Cedar Cove Court, said Sgt<PERSON> of the Harris County Sheriff's Department. \n\nAn investigation found that <PERSON>, 41, was shot by his son, who was sitting in the back seat of the man's Toyota 4-Runner, said <PERSON><PERSON> <PERSON>. The shooting took place outside the home of <PERSON><PERSON><PERSON><PERSON>'s ex-wife, where the boy lives with his mother and a 7-year-old brother. \n\n\"We're not certain of anything until we finish our investigation,\" <PERSON> said. \"The information we have at this time is that the 10-year-old did fire the weapon.\" \n\nThe mother and the 7-year-old were inside the house when the shooting occurred, said <PERSON>. \n\n<PERSON> said the gun belonged to the boy's mother. After firing shots through the back seat, the boy exited the back of the vehicle and continued to fire at the car. \n\nThe man died on the way to Memorial Herman Hospital. \n\n<PERSON><PERSON><PERSON><PERSON> was a doctor at the University of Texas Medical Branch. \n\nThe man and woman shared custody of the children. \n\n<PERSON> said the mother and the boy were still in the home talking to investigators Friday night. \n\n<PERSON><PERSON><PERSON><PERSON><PERSON> described the family as being quiet and keeping to themselves. \n\n<PERSON>, 17, was walking in the neighborhood when he heard gunshots. \"We've had a suicide in this neighborhood once, but never anything like this,\" <PERSON> said.", "questions": ["What happened Friday?", "Where was he at?", "At his dad's house?", "Where?", "when?", "What time?", "whose gun was it?", "What did the child do after he shot his dad?", "DId he live?", "Where did he work?", "Where was the mother?", "was anyone with her?", "who?"], "answers": {"input_text": ["10-year-old boy fatally shot his father", "in the front seat of a SUV", "no", "outside the home of <PERSON><PERSON><PERSON><PERSON>'s ex-wife", "Friday", "3pm", "belonged to the boy's mother", "He exited the back of the vehicle and continued to fire at the car", "No", "at the University of Texas Medical Branch", "inside the house", "Yes", "the 7-year-old"], "answer_start": [19, 103, 538, 527, 58, 223, 917, 990, 1065, 1141, 834, 813, 813], "answer_end": [59, 130, 565, 565, 65, 227, 946, 1061, 1078, 1183, 850, 828, 829]}}}, {"input": {"input": "Who was excommunicated?", "output": "<PERSON><PERSON>", "expected": "<PERSON><PERSON>"}, "expected": 1, "metadata": {"source": "<PERSON><PERSON>", "story": "CHAPTER FIFTY FIVE. \n\nWAITING. \n\nThe lengthening sunny days went on without bringing either what <PERSON><PERSON><PERSON> most desired or what she most dreaded. They brought no sign from <PERSON><PERSON><PERSON><PERSON>, and, in spite of special watch on the part of the Government, no revelation of the suspected conspiracy. But they brought other things which touched her closely, and bridged the phantom-crowded space of anxiety with active sympathy in immediate trial. They brought the spreading Plague and the Excommunication of <PERSON><PERSON><PERSON><PERSON>. \n\nBoth these events tended to arrest her incipient alienation from the Frate, and to rivet again her attachment to the man who had opened to her the new life of duty, and who seemed now to be worsted in the fight for principle against profligacy. For <PERSON><PERSON><PERSON> could not carry from day to day into the abodes of pestilence and misery the sublime excitement of a gladness that, since such anguish existed, she too existed to make some of the anguish less bitter, without remembering that she owed this transcendent moral life to <PERSON><PERSON>. She could not witness the silencing and excommunication of a man whose distinction from the great mass of the clergy lay, not in any heretical belief, not in his superstitions, but in the energy with which he sought to make the Christian life a reality, without feeling herself drawn strongly to his side. \n\nFar on in the hot days of June the Excommunication, for some weeks arrived from Rome, was solemnly published in the Duomo. <PERSON><PERSON><PERSON> went to witness the scene, that the resistance it inspired might invigorate that sympathy with <PERSON><PERSON><PERSON><PERSON> which was one source of her strength. It was in memorable contrast with the scene she had been accustomed to witness there. ", "questions": ["Who was excommunicated?", "Was he a heretic?", "Was he superstitious?", "Why was he excommunicated then?", "Who was inspired by this man?", "Where was the Excommunication published?", "When?", "How long was it published?", "What was <PERSON><PERSON><PERSON> looking for?", "What else was she looking for?", "What did the summer days bring?", "Who was she alienated from?", "Did Romola fight the plague?"], "answers": {"input_text": ["<PERSON><PERSON>", "no", "no", "unknown", "Romola", "in the Duomo", "June", "for some weeks", "a sign from Baldassarre", "sympathy with <PERSON><PERSON><PERSON><PERSON>", "Plague", "the Frate", "no"], "answer_start": [1032, 1168, 1197, -1, 991, 1385, 1361, 1405, 144, 1511, 434, 544, 714], "answer_end": [1110, 1195, 1221, -1, 1044, 1475, 1476, 1437, 180, 1590, 505, 583, 753]}}}, {"input": {"input": "Who was excommunicated?", "output": "no", "expected": "<PERSON><PERSON>"}, "expected": 0, "metadata": {"source": "<PERSON><PERSON>", "story": "CHAPTER FIFTY FIVE. \n\nWAITING. \n\nThe lengthening sunny days went on without bringing either what <PERSON><PERSON><PERSON> most desired or what she most dreaded. They brought no sign from <PERSON><PERSON><PERSON><PERSON>, and, in spite of special watch on the part of the Government, no revelation of the suspected conspiracy. But they brought other things which touched her closely, and bridged the phantom-crowded space of anxiety with active sympathy in immediate trial. They brought the spreading Plague and the Excommunication of <PERSON><PERSON><PERSON><PERSON>. \n\nBoth these events tended to arrest her incipient alienation from the Frate, and to rivet again her attachment to the man who had opened to her the new life of duty, and who seemed now to be worsted in the fight for principle against profligacy. For <PERSON><PERSON><PERSON> could not carry from day to day into the abodes of pestilence and misery the sublime excitement of a gladness that, since such anguish existed, she too existed to make some of the anguish less bitter, without remembering that she owed this transcendent moral life to <PERSON><PERSON>. She could not witness the silencing and excommunication of a man whose distinction from the great mass of the clergy lay, not in any heretical belief, not in his superstitions, but in the energy with which he sought to make the Christian life a reality, without feeling herself drawn strongly to his side. \n\nFar on in the hot days of June the Excommunication, for some weeks arrived from Rome, was solemnly published in the Duomo. <PERSON><PERSON><PERSON> went to witness the scene, that the resistance it inspired might invigorate that sympathy with <PERSON><PERSON><PERSON><PERSON> which was one source of her strength. It was in memorable contrast with the scene she had been accustomed to witness there. ", "questions": ["Who was excommunicated?", "Was he a heretic?", "Was he superstitious?", "Why was he excommunicated then?", "Who was inspired by this man?", "Where was the Excommunication published?", "When?", "How long was it published?", "What was <PERSON><PERSON><PERSON> looking for?", "What else was she looking for?", "What did the summer days bring?", "Who was she alienated from?", "Did Romola fight the plague?"], "answers": {"input_text": ["<PERSON><PERSON>", "no", "no", "unknown", "Romola", "in the Duomo", "June", "for some weeks", "a sign from Baldassarre", "sympathy with <PERSON><PERSON><PERSON><PERSON>", "Plague", "the Frate", "no"], "answer_start": [1032, 1168, 1197, -1, 991, 1385, 1361, 1405, 144, 1511, 434, 544, 714], "answer_end": [1110, 1195, 1221, -1, 1044, 1475, 1476, 1437, 180, 1590, 505, 583, 753]}}}, {"input": {"input": "Who was excommunicated?", "output": "no Fra Girolamo no", "expected": "<PERSON><PERSON>"}, "expected": 0.6, "metadata": {"source": "<PERSON><PERSON>", "story": "CHAPTER FIFTY FIVE. \n\nWAITING. \n\nThe lengthening sunny days went on without bringing either what <PERSON><PERSON><PERSON> most desired or what she most dreaded. They brought no sign from <PERSON><PERSON><PERSON><PERSON>, and, in spite of special watch on the part of the Government, no revelation of the suspected conspiracy. But they brought other things which touched her closely, and bridged the phantom-crowded space of anxiety with active sympathy in immediate trial. They brought the spreading Plague and the Excommunication of <PERSON><PERSON><PERSON><PERSON>. \n\nBoth these events tended to arrest her incipient alienation from the Frate, and to rivet again her attachment to the man who had opened to her the new life of duty, and who seemed now to be worsted in the fight for principle against profligacy. For <PERSON><PERSON><PERSON> could not carry from day to day into the abodes of pestilence and misery the sublime excitement of a gladness that, since such anguish existed, she too existed to make some of the anguish less bitter, without remembering that she owed this transcendent moral life to <PERSON><PERSON>. She could not witness the silencing and excommunication of a man whose distinction from the great mass of the clergy lay, not in any heretical belief, not in his superstitions, but in the energy with which he sought to make the Christian life a reality, without feeling herself drawn strongly to his side. \n\nFar on in the hot days of June the Excommunication, for some weeks arrived from Rome, was solemnly published in the Duomo. <PERSON><PERSON><PERSON> went to witness the scene, that the resistance it inspired might invigorate that sympathy with <PERSON><PERSON><PERSON><PERSON> which was one source of her strength. It was in memorable contrast with the scene she had been accustomed to witness there. ", "questions": ["Who was excommunicated?", "Was he a heretic?", "Was he superstitious?", "Why was he excommunicated then?", "Who was inspired by this man?", "Where was the Excommunication published?", "When?", "How long was it published?", "What was <PERSON><PERSON><PERSON> looking for?", "What else was she looking for?", "What did the summer days bring?", "Who was she alienated from?", "Did Romola fight the plague?"], "answers": {"input_text": ["<PERSON><PERSON>", "no", "no", "unknown", "Romola", "in the Duomo", "June", "for some weeks", "a sign from Baldassarre", "sympathy with <PERSON><PERSON><PERSON><PERSON>", "Plague", "the Frate", "no"], "answer_start": [1032, 1168, 1197, -1, 991, 1385, 1361, 1405, 144, 1511, 434, 544, 714], "answer_end": [1110, 1195, 1221, -1, 1044, 1475, 1476, 1437, 180, 1590, 505, 583, 753]}}}]